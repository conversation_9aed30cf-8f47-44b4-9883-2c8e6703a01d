# 🚀 AI Research Assistant Setup Checklist

## 📋 **Phase 1: Foundation Setup**

### **✅ System Requirements Check**
- [x] RTX 4070 16GB GPU detected
- [x] Docker 28.2.2 installed
- [x] Python 3.13.5 installed  
- [x] Node.js 22.16.0 installed
- [ ] Ollama installed
- [ ] System test passed

### **🔧 Installation Steps**

#### **Step 1: Install Ollama**
```bash
# Download from: https://ollama.com/download/windows
# Install the .exe file
# Verify installation:
ollama --version
```

#### **Step 2: Install Python Dependencies**
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
pip install transformers whisper-openai bark-voice-cloning
pip install fastapi uvicorn websockets requests
pip install livekit-agents speechbrain
```

#### **Step 3: Download Highest Quality Models**
```bash
# LLM Models (choose based on VRAM)
ollama pull deepseek-r1:14b    # 12GB VRAM - RECOMMENDED
ollama pull nomic-embed-text   # Embeddings
ollama pull deepseek-coder:6.7b # Code tasks

# Test models
ollama run deepseek-r1:14b "Hello, test message"
```

#### **Step 4: Download Voice Models**
```python
# Run this Python script to download voice models
import whisper
import torch
from bark import preload_models

# Download Whisper (highest quality)
model = whisper.load_model("large-v3")
print("✅ Whisper large-v3 downloaded")

# Download Bark (most natural TTS)
preload_models()
print("✅ Bark models downloaded")
```

### **🧪 Testing After Each Step**

Run our test script after each installation:
```bash
python test_system_components.py
```

## 📋 **Phase 2: TEN Framework Setup**

### **Step 1: Configure TEN Framework**
```bash
cd ten-framework-main/ai_agents
cp .env.example .env.local
```

### **Step 2: Create Local Configuration**
Edit `.env.local`:
```bash
# Local-only configuration (no external APIs)
USE_LOCAL_WEBRTC=true
STT_PROVIDER=whisper
TTS_PROVIDER=bark
LLM_PROVIDER=ollama
OLLAMA_URL=http://localhost:11434
WHISPER_MODEL=large-v3
BARK_VOICE=v2/en_speaker_6
```

### **Step 3: Build and Test**
```bash
# Build TEN Framework
docker compose -f docker-compose.local.yml build

# Start services
docker compose -f docker-compose.local.yml up -d

# Test endpoints
curl http://localhost:49483/health
curl http://localhost:3000/health
```

## 📋 **Phase 3: AgenticSeek Integration**

### **Step 1: Configure AgenticSeek**
```bash
cd agenticSeek-main
cp .env.example .env.local
```

### **Step 2: Local Configuration**
Edit `config.ini`:
```ini
[MAIN]
is_local = True
provider_name = ollama
provider_model = deepseek-r1:14b
provider_server_address = 127.0.0.1:11434
```

### **Step 3: Start and Test**
```bash
./start_services.sh full
# Test: http://localhost:8080
```

## 📋 **Phase 4: Integration Testing**

### **Component Tests**
- [ ] Ollama models responding
- [ ] TEN Framework UI accessible
- [ ] AgenticSeek web interface working
- [ ] Voice models loaded
- [ ] GPU utilization working

### **Integration Tests**
- [ ] TEN ↔ Ollama communication
- [ ] AgenticSeek ↔ Ollama communication  
- [ ] MCP connections working
- [ ] Voice pipeline functional
- [ ] End-to-end conversation test

## 📋 **Quality Assurance Checklist**

### **Performance Targets**
- [ ] STT latency < 2 seconds
- [ ] LLM response < 5 seconds
- [ ] TTS generation < 3 seconds
- [ ] Total conversation latency < 10 seconds
- [ ] GPU memory usage < 14GB

### **Quality Targets**
- [ ] STT accuracy > 95%
- [ ] Natural TTS voice quality
- [ ] Coherent LLM responses
- [ ] Successful web research
- [ ] File operations working

## 🚨 **Troubleshooting Guide**

### **Common Issues**
1. **Ollama not starting**: Check Windows Defender, restart service
2. **GPU not detected**: Update NVIDIA drivers, check CUDA
3. **Docker issues**: Restart Docker Desktop, check WSL2
4. **Port conflicts**: Check if ports 3000, 8080, 11434, 49483 are free
5. **Model download fails**: Check internet, disk space, retry

### **Performance Issues**
1. **Slow responses**: Check GPU utilization, reduce model size
2. **Memory errors**: Close other applications, use smaller models
3. **Audio issues**: Check microphone permissions, audio drivers

## 📊 **Success Criteria**

### **Phase 1 Complete When:**
- [ ] All system tests pass
- [ ] Ollama models loaded and responding
- [ ] Voice models downloaded and working
- [ ] No external API dependencies

### **Phase 2 Complete When:**
- [ ] TEN Framework running locally
- [ ] Web interface accessible
- [ ] Local voice processing working
- [ ] Integration with Ollama confirmed

### **Phase 3 Complete When:**
- [ ] AgenticSeek running with Ollama
- [ ] Web research capabilities working
- [ ] MCP connections established
- [ ] File operations functional

### **Final Success:**
- [ ] Voice input → research → voice output working
- [ ] Complete conversation flow functional
- [ ] No external API calls made
- [ ] System runs offline
- [ ] Performance targets met

## 🎯 **Next Steps After Setup**

1. **Customize voice settings**
2. **Train on specific research domains**
3. **Add custom tools and capabilities**
4. **Optimize for your specific use cases**
5. **Create backup and deployment scripts**

---

**🔄 Remember: Test after each step, ask questions if stuck!**
