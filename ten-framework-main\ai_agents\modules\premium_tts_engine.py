#!/usr/bin/env python3
"""
PREMIUM TTS ENGINE - ALL BEST SYSTEMS INTEGRATED
Supports ChatTTS, ChatterboxTTS, Edge-TTS, and advanced features
"""

import time
import subprocess
import threading
import pygame
import torch
import numpy as np
import asyncio
import tempfile
import os
import warnings
warnings.filterwarnings("ignore")

# Import all premium TTS systems
try:
    import ChatTTS
    CHATTTS_AVAILABLE = True
except ImportError:
    CHATTTS_AVAILABLE = False
    print("⚠️ ChatTTS not available")

try:
    from chatterbox.tts import ChatterboxTTS
    CHATTERBOX_AVAILABLE = True
except ImportError:
    CHATTERBOX_AVAILABLE = False
    print("⚠️ ChatterboxTTS not available")

try:
    import edge_tts
    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False
    print("⚠️ Edge-TTS not available")

try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False
    print("⚠️ pyttsx3 not available")

class PremiumTTSEngine:
    """Premium TTS Engine with ALL best systems"""
    
    def __init__(self, config):
        self.config = config
        self.is_speaking = False
        self.should_interrupt = False
        
        # Initialize all TTS systems
        self._init_premium_tts()
        self._init_audio()
        
    def _init_premium_tts(self):
        """Initialize all premium TTS systems"""
        print("🚀 INITIALIZING PREMIUM TTS SYSTEMS")
        print("=" * 50)
        
        # Initialize ChatTTS (Best for conversation)
        if CHATTTS_AVAILABLE:
            try:
                print("📱 Loading ChatTTS (Premium Conversation)...")
                self.chat_tts = ChatTTS.Chat()
                self.chat_tts.load(compile=False, source="huggingface")
                print("✅ ChatTTS ready - Premium conversation quality")
                self.primary_tts = "chattts"
            except Exception as e:
                print(f"❌ ChatTTS failed: {e}")
                self.chat_tts = None
        else:
            self.chat_tts = None
            
        # Initialize ChatterboxTTS (ElevenLabs alternative)
        if self.config.use_chatterbox and CHATTERBOX_AVAILABLE:
            try:
                print(f"📱 Loading ChatterboxTTS (ElevenLabs Alternative)...")
                self.chatterbox_tts = ChatterboxTTS.from_pretrained(
                    device=self.config.device
                )
                print(f"✅ ChatterboxTTS ready on {self.config.device}")
                if not hasattr(self, 'primary_tts'):
                    self.primary_tts = "chatterbox"
            except Exception as e:
                print(f"❌ ChatterboxTTS failed: {e}")
                self.chatterbox_tts = None
        else:
            self.chatterbox_tts = None
            
        # Initialize Edge-TTS (Microsoft Neural Voices)
        if EDGE_TTS_AVAILABLE:
            print("✅ Edge-TTS ready - Microsoft Neural Voices")
            self.edge_tts_available = True
            if not hasattr(self, 'primary_tts'):
                self.primary_tts = "edge"
        else:
            self.edge_tts_available = False
            
        # Initialize pyttsx3 (System fallback)
        if PYTTSX3_AVAILABLE:
            try:
                self.pyttsx3_engine = pyttsx3.init()
                voices = self.pyttsx3_engine.getProperty('voices')
                if voices:
                    # Select best female voice
                    for voice in voices:
                        if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                            self.pyttsx3_engine.setProperty('voice', voice.id)
                            break
                self.pyttsx3_engine.setProperty('rate', 180)
                self.pyttsx3_engine.setProperty('volume', 1.0)
                print("✅ pyttsx3 ready - System fallback")
                if not hasattr(self, 'primary_tts'):
                    self.primary_tts = "pyttsx3"
            except Exception as e:
                print(f"❌ pyttsx3 failed: {e}")
                self.pyttsx3_engine = None
        else:
            self.pyttsx3_engine = None
            
        # Set fallback if no primary TTS
        if not hasattr(self, 'primary_tts'):
            self.primary_tts = "windows"
            
        print(f"🎵 PRIMARY TTS: {self.primary_tts.upper()}")
        print("=" * 50)
        
    def _init_audio(self):
        """Initialize premium audio playback"""
        try:
            pygame.mixer.init(
                frequency=22050,  # Higher quality
                size=-16,
                channels=2,
                buffer=2048  # Larger buffer for better quality
            )
            print("✅ Premium audio playback ready (22kHz)")
        except Exception as e:
            print(f"⚠️ Audio warning: {e}")
            
    def speak(self, text: str):
        """Main speak method with premium quality"""
        if not text.strip():
            return
            
        print(f"🗣️ Speaking ({self.primary_tts}): {text[:50]}...")
        
        self.is_speaking = True
        self.should_interrupt = False
        
        try:
            if self.primary_tts == "chattts" and self.chat_tts:
                self._speak_chattts(text)
            elif self.primary_tts == "chatterbox" and self.chatterbox_tts:
                self._speak_chatterbox(text)
            elif self.primary_tts == "edge" and self.edge_tts_available:
                self._speak_edge_tts(text)
            elif self.primary_tts == "pyttsx3" and self.pyttsx3_engine:
                self._speak_pyttsx3(text)
            else:
                self._speak_windows_premium(text)
        except Exception as e:
            print(f"❌ TTS Error: {e}")
            self._speak_windows_premium(text)
        finally:
            self.is_speaking = False
            
    def _speak_chattts(self, text: str):
        """ChatTTS - Premium conversation quality"""
        try:
            # Generate with ChatTTS
            texts = [text]
            wavs = self.chat_tts.infer(texts, use_decoder=True)
            
            if wavs and len(wavs) > 0:
                # Convert to audio
                audio_data = wavs[0]
                
                # Save to temporary file
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                    import torchaudio
                    torchaudio.save(tmp_file.name, torch.from_numpy(audio_data).unsqueeze(0), 22050)
                    
                    # Play with interruption support
                    self._play_audio_file(tmp_file.name)
                    
                # Cleanup
                os.unlink(tmp_file.name)
            else:
                raise Exception("ChatTTS generation failed")
                
        except Exception as e:
            print(f"❌ ChatTTS error: {e}")
            raise
            
    def _speak_chatterbox(self, text: str):
        """ChatterboxTTS - ElevenLabs alternative"""
        try:
            # Generate with ChatterboxTTS
            audio_tensor = self.chatterbox_tts.generate(text)
            
            if audio_tensor is not None:
                # Convert to numpy
                if isinstance(audio_tensor, torch.Tensor):
                    audio_data = audio_tensor.cpu().numpy()
                else:
                    audio_data = audio_tensor
                    
                # Save to temporary file
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                    import soundfile as sf
                    sf.write(tmp_file.name, audio_data, 22050)
                    
                    # Play with interruption support
                    self._play_audio_file(tmp_file.name)
                    
                # Cleanup
                os.unlink(tmp_file.name)
            else:
                raise Exception("ChatterboxTTS generation failed")
                
        except Exception as e:
            print(f"❌ ChatterboxTTS error: {e}")
            raise
            
    def _speak_edge_tts(self, text: str):
        """Edge-TTS - Microsoft Neural Voices"""
        try:
            async def generate_speech():
                # Use premium female voice
                voice = "en-US-AriaNeural"  # Premium neural voice
                communicate = edge_tts.Communicate(text, voice)
                
                with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as tmp_file:
                    await communicate.save(tmp_file.name)
                    return tmp_file.name
                    
            # Run async function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            audio_file = loop.run_until_complete(generate_speech())
            loop.close()
            
            # Play with interruption support
            self._play_audio_file(audio_file)
            
            # Cleanup
            os.unlink(audio_file)
            
        except Exception as e:
            print(f"❌ Edge-TTS error: {e}")
            raise
            
    def _speak_pyttsx3(self, text: str):
        """pyttsx3 - Enhanced system TTS"""
        try:
            # Save to file for better quality
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                self.pyttsx3_engine.save_to_file(text, tmp_file.name)
                self.pyttsx3_engine.runAndWait()
                
                # Play with interruption support
                self._play_audio_file(tmp_file.name)
                
            # Cleanup
            os.unlink(tmp_file.name)
            
        except Exception as e:
            print(f"❌ pyttsx3 error: {e}")
            raise
            
    def _speak_windows_premium(self, text: str):
        """Premium Windows TTS with better voices"""
        try:
            clean_text = text.replace('"', '').replace("'", "")
            clean_text = ' '.join(clean_text.split())
            
            # Use premium Windows voice
            ps_cmd = f'''
            Add-Type -AssemblyName System.Speech;
            $synth = New-Object System.Speech.Synthesis.SpeechSynthesizer;
            $voices = $synth.GetInstalledVoices();
            foreach ($voice in $voices) {{
                if ($voice.VoiceInfo.Name -like "*Zira*" -or $voice.VoiceInfo.Name -like "*Eva*") {{
                    $synth.SelectVoice($voice.VoiceInfo.Name);
                    break;
                }}
            }}
            $synth.Rate = 1;
            $synth.Volume = 100;
            $synth.Speak("{clean_text}");
            '''
            
            process = subprocess.Popen(
                ['powershell', '-Command', ps_cmd],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            # Monitor for interruption
            while process.poll() is None:
                if self.should_interrupt:
                    process.terminate()
                    print("🛑 Speech interrupted!")
                    return
                time.sleep(0.05)
                
        except Exception as e:
            print(f"❌ Windows TTS error: {e}")
            raise
            
    def _play_audio_file(self, file_path: str):
        """Play audio file with interruption support"""
        try:
            pygame.mixer.music.load(file_path)
            pygame.mixer.music.play()
            
            # Monitor for interruption
            while pygame.mixer.music.get_busy():
                if self.should_interrupt:
                    pygame.mixer.music.stop()
                    print("🛑 Audio interrupted!")
                    return
                time.sleep(0.05)
                
        except Exception as e:
            print(f"❌ Audio playback error: {e}")
            
    def interrupt(self):
        """Interrupt current speech"""
        self.should_interrupt = True
        
    def is_busy(self) -> bool:
        """Check if TTS is currently speaking"""
        return self.is_speaking
        
    def get_status(self) -> str:
        """Get TTS status"""
        return f"{self.primary_tts.title()} TTS"
