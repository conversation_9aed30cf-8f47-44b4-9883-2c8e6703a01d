FROM --platform=linux/amd64 python:3.12-slim
ENV DEBIAN_FRONTEND=noninteractive

# Install essential packages and Chrome dependencies
RUN apt-get update -y && apt-get install -y \
    wget \
    gnupg2 \
    ca-certificates \
    unzip \
    xvfb \
    libxss1 \
    libappindicator1 \
    fonts-liberation \
    libnss3 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdrm2 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    xdg-utils \
    dbus \
    gcc \
    g++ \
    gfortran \
    libportaudio2 \
    portaudio19-dev \
    ffmpeg \
    libavcodec-dev \
    libavformat-dev \
    libavutil-dev \
    python3 \
    python3-pip \
    libasound2 \
    libatk-bridge2.0-0 \
    libgtk-4-1 \
    alsa-utils \
    && rm -rf /var/lib/apt/lists/*

ENV CHROME_TESTING_VERSION=134.0.6998.88
ENV DISPLAY=:99

WORKDIR /app

# Install Chrome and ChromeDriver
RUN set -eux; \
    wget -qO /tmp/chrome.zip \
      "https://storage.googleapis.com/chrome-for-testing-public/${CHROME_TESTING_VERSION}/linux64/chrome-linux64.zip"; \
    unzip -q /tmp/chrome.zip -d /opt; \
    rm /tmp/chrome.zip; \
    ln -s /opt/chrome-linux64/chrome /usr/local/bin/google-chrome; \
    ln -s /opt/chrome-linux64/chrome /usr/local/bin/chrome; \
    mkdir -p /opt/chrome; \
    ln -s /opt/chrome-linux64/chrome /opt/chrome/chrome; \
    chmod +x /opt/chrome/chrome; \
    google-chrome --version

RUN set -eux; \
    wget -qO /tmp/chromedriver.zip \
      "https://storage.googleapis.com/chrome-for-testing-public/${CHROME_TESTING_VERSION}/linux64/chromedriver-linux64.zip"; \
    unzip -q /tmp/chromedriver.zip -d /tmp; \
    mv /tmp/chromedriver-linux64/chromedriver /usr/local/bin; \
    rm /tmp/chromedriver.zip; \
    chmod +x /usr/local/bin/chromedriver; \
    chromedriver --version

# Upgrade pip and install Python dependencies
RUN pip3 install --upgrade pip setuptools wheel

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Create necessary directories
RUN mkdir -p /opt/workspace /app/work_dir /app/data
RUN chmod 1777 /tmp

# Copy application code
COPY . .

# Set environment variables for local operation
ENV WORK_DIR=/app/work_dir
ENV LOCAL_MODE=true
ENV BROWSER_HEADLESS=true
ENV PYTHONPATH=/app

# Expose ports
EXPOSE 8080 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python3 -c "import requests; requests.get('http://localhost:8080/health')" || exit 1

# Default command - can be overridden
CMD ["python3", "api.py"]
