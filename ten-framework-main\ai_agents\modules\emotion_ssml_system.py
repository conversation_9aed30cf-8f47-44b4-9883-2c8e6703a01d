#!/usr/bin/env python3
"""
EMOTION & SSML SYSTEM - ADVANCED SPEECH CONTROL
Implements SSML support, emotion detection, and prosody control
"""

import re
import torch
import numpy as np
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings("ignore")

# Try to import emotion detection
try:
    from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
    EMOTION_DETECTION_AVAILABLE = True
except ImportError:
    EMOTION_DETECTION_AVAILABLE = False

try:
    import edge_tts
    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False

class EmotionSSMLSystem:
    """Advanced emotion detection and SSML processing system"""
    
    def __init__(self, config):
        self.config = config
        
        # Initialize emotion detection
        self._init_emotion_detection()
        
        # SSML templates and settings
        self._init_ssml_system()
        
        # Emotion-to-voice mapping
        self._init_emotion_mapping()
        
    def _init_emotion_detection(self):
        """Initialize emotion detection system"""
        print("🎭 INITIALIZING EMOTION DETECTION")
        print("=" * 50)
        
        if EMOTION_DETECTION_AVAILABLE:
            try:
                print("📱 Loading Emotion Detection Model...")
                
                # Load emotion detection pipeline
                self.emotion_detector = pipeline(
                    "text-classification",
                    model="j-hartmann/emotion-english-distilroberta-base",
                    device=0 if torch.cuda.is_available() else -1
                )
                
                print("✅ Emotion Detection ready")
                self.emotion_available = True
                
            except Exception as e:
                print(f"❌ Emotion Detection failed: {e}")
                self.emotion_detector = None
                self.emotion_available = False
        else:
            print("❌ Emotion Detection not available")
            self.emotion_detector = None
            self.emotion_available = False
            
        print("=" * 50)
        
    def _init_ssml_system(self):
        """Initialize SSML processing system"""
        print("📝 Initializing SSML System...")
        
        # SSML emotion templates
        self.ssml_templates = {
            'joy': '<prosody rate="medium" pitch="+10%" volume="loud">{text}</prosody>',
            'excitement': '<prosody rate="fast" pitch="+15%" volume="loud">{text}</prosody>',
            'happiness': '<prosody rate="medium" pitch="+5%" volume="medium">{text}</prosody>',
            'sadness': '<prosody rate="slow" pitch="-10%" volume="soft">{text}</prosody>',
            'anger': '<prosody rate="fast" pitch="+5%" volume="loud">{text}</prosody>',
            'fear': '<prosody rate="fast" pitch="+20%" volume="soft">{text}</prosody>',
            'surprise': '<prosody rate="medium" pitch="+25%" volume="medium">{text}</prosody>',
            'disgust': '<prosody rate="slow" pitch="-5%" volume="medium">{text}</prosody>',
            'neutral': '<prosody rate="medium" pitch="medium" volume="medium">{text}</prosody>',
            'calm': '<prosody rate="slow" pitch="medium" volume="soft">{text}</prosody>',
            'confident': '<prosody rate="medium" pitch="medium" volume="loud">{text}</prosody>',
            'whisper': '<prosody rate="slow" pitch="low" volume="x-soft">{text}</prosody>',
            'emphasis': '<emphasis level="strong">{text}</emphasis>',
            'break_short': '<break time="0.5s"/>',
            'break_medium': '<break time="1s"/>',
            'break_long': '<break time="2s"/>'
        }
        
        # Prosody settings
        self.prosody_settings = {
            'rate': ['x-slow', 'slow', 'medium', 'fast', 'x-fast'],
            'pitch': ['x-low', 'low', 'medium', 'high', 'x-high'],
            'volume': ['silent', 'x-soft', 'soft', 'medium', 'loud', 'x-loud']
        }
        
        print("✅ SSML System ready")
        
    def _init_emotion_mapping(self):
        """Initialize emotion to voice characteristic mapping"""
        self.emotion_voice_mapping = {
            'joy': {
                'rate': 'medium',
                'pitch': '+10%',
                'volume': 'loud',
                'style': 'cheerful'
            },
            'sadness': {
                'rate': 'slow',
                'pitch': '-10%',
                'volume': 'soft',
                'style': 'sad'
            },
            'anger': {
                'rate': 'fast',
                'pitch': '+5%',
                'volume': 'loud',
                'style': 'angry'
            },
            'fear': {
                'rate': 'fast',
                'pitch': '+20%',
                'volume': 'soft',
                'style': 'fearful'
            },
            'surprise': {
                'rate': 'medium',
                'pitch': '+25%',
                'volume': 'medium',
                'style': 'excited'
            },
            'disgust': {
                'rate': 'slow',
                'pitch': '-5%',
                'volume': 'medium',
                'style': 'disgusted'
            },
            'neutral': {
                'rate': 'medium',
                'pitch': 'medium',
                'volume': 'medium',
                'style': 'neutral'
            }
        }
        
    def detect_emotion(self, text: str) -> Dict:
        """Detect emotion from text"""
        try:
            if not self.emotion_available or not self.emotion_detector:
                return {'emotion': 'neutral', 'confidence': 0.5}
                
            # Get emotion prediction
            result = self.emotion_detector(text)
            
            if result and len(result) > 0:
                emotion_data = result[0]
                emotion = emotion_data['label'].lower()
                confidence = emotion_data['score']
                
                print(f"🎭 Detected emotion: {emotion} ({confidence:.2f})")
                
                return {
                    'emotion': emotion,
                    'confidence': confidence,
                    'all_emotions': result
                }
            else:
                return {'emotion': 'neutral', 'confidence': 0.5}
                
        except Exception as e:
            print(f"❌ Emotion detection failed: {e}")
            return {'emotion': 'neutral', 'confidence': 0.5}
            
    def apply_emotion_to_text(self, text: str, emotion: str = None, confidence: float = 1.0) -> str:
        """Apply emotion-based SSML to text"""
        try:
            # Detect emotion if not provided
            if emotion is None:
                emotion_data = self.detect_emotion(text)
                emotion = emotion_data['emotion']
                confidence = emotion_data['confidence']
                
            # Only apply emotion if confidence is high enough
            if confidence < 0.6:
                emotion = 'neutral'
                
            # Apply SSML template
            if emotion in self.ssml_templates:
                ssml_text = self.ssml_templates[emotion].format(text=text)
                print(f"🎵 Applied {emotion} emotion to text")
                return ssml_text
            else:
                return text
                
        except Exception as e:
            print(f"❌ Emotion application failed: {e}")
            return text
            
    def create_ssml_document(self, text: str, emotion: str = None, custom_prosody: Dict = None) -> str:
        """Create complete SSML document"""
        try:
            # Detect emotion if not provided
            if emotion is None:
                emotion_data = self.detect_emotion(text)
                emotion = emotion_data['emotion']
                
            # Get voice characteristics for emotion
            voice_settings = self.emotion_voice_mapping.get(emotion, self.emotion_voice_mapping['neutral'])
            
            # Override with custom prosody if provided
            if custom_prosody:
                voice_settings.update(custom_prosody)
                
            # Create SSML document
            ssml_doc = f'''<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">
    <voice name="en-US-AriaNeural">
        <prosody rate="{voice_settings['rate']}" pitch="{voice_settings['pitch']}" volume="{voice_settings['volume']}">
            {text}
        </prosody>
    </voice>
</speak>'''
            
            return ssml_doc
            
        except Exception as e:
            print(f"❌ SSML document creation failed: {e}")
            return f'<speak>{text}</speak>'
            
    def parse_emotion_markers(self, text: str) -> Tuple[str, List[Dict]]:
        """Parse emotion markers from text"""
        try:
            # Pattern to match emotion markers like [happy], [sad], etc.
            emotion_pattern = r'\[(\w+)\]'
            
            emotions = []
            clean_text = text
            
            # Find all emotion markers
            matches = re.finditer(emotion_pattern, text)
            
            for match in matches:
                emotion = match.group(1).lower()
                start_pos = match.start()
                end_pos = match.end()
                
                emotions.append({
                    'emotion': emotion,
                    'start': start_pos,
                    'end': end_pos,
                    'marker': match.group(0)
                })
                
                # Remove marker from text
                clean_text = clean_text.replace(match.group(0), '', 1)
                
            return clean_text, emotions
            
        except Exception as e:
            print(f"❌ Emotion marker parsing failed: {e}")
            return text, []
            
    def apply_contextual_emotions(self, text: str) -> str:
        """Apply contextual emotion detection and SSML"""
        try:
            # Parse emotion markers first
            clean_text, explicit_emotions = self.parse_emotion_markers(text)
            
            if explicit_emotions:
                # Apply explicit emotions
                result_text = clean_text
                for emotion_data in explicit_emotions:
                    emotion = emotion_data['emotion']
                    if emotion in self.ssml_templates:
                        # Apply emotion to the entire text for now
                        result_text = self.ssml_templates[emotion].format(text=result_text)
                        break  # Use first emotion found
                        
                return result_text
            else:
                # Auto-detect emotion
                return self.apply_emotion_to_text(clean_text)
                
        except Exception as e:
            print(f"❌ Contextual emotion application failed: {e}")
            return text
            
    def enhance_speech_naturalness(self, text: str) -> str:
        """Enhance speech naturalness with pauses and emphasis"""
        try:
            enhanced_text = text
            
            # Add pauses after punctuation
            enhanced_text = re.sub(r'\.', '.<break time="0.5s"/>', enhanced_text)
            enhanced_text = re.sub(r',', ',<break time="0.3s"/>', enhanced_text)
            enhanced_text = re.sub(r';', ';<break time="0.4s"/>', enhanced_text)
            enhanced_text = re.sub(r'!', '!<break time="0.6s"/>', enhanced_text)
            enhanced_text = re.sub(r'\?', '?<break time="0.6s"/>', enhanced_text)
            
            # Add emphasis to important words
            emphasis_words = ['important', 'critical', 'urgent', 'amazing', 'incredible', 'fantastic']
            for word in emphasis_words:
                pattern = r'\b' + re.escape(word) + r'\b'
                replacement = f'<emphasis level="strong">{word}</emphasis>'
                enhanced_text = re.sub(pattern, replacement, enhanced_text, flags=re.IGNORECASE)
                
            return enhanced_text
            
        except Exception as e:
            print(f"❌ Speech enhancement failed: {e}")
            return text
            
    def get_emotion_statistics(self, texts: List[str]) -> Dict:
        """Get emotion statistics for a list of texts"""
        try:
            if not self.emotion_available:
                return {'error': 'Emotion detection not available'}
                
            emotion_counts = {}
            total_confidence = 0
            
            for text in texts:
                emotion_data = self.detect_emotion(text)
                emotion = emotion_data['emotion']
                confidence = emotion_data['confidence']
                
                if emotion in emotion_counts:
                    emotion_counts[emotion] += 1
                else:
                    emotion_counts[emotion] = 1
                    
                total_confidence += confidence
                
            avg_confidence = total_confidence / len(texts) if texts else 0
            
            return {
                'emotion_distribution': emotion_counts,
                'total_texts': len(texts),
                'average_confidence': avg_confidence,
                'most_common_emotion': max(emotion_counts, key=emotion_counts.get) if emotion_counts else 'neutral'
            }
            
        except Exception as e:
            print(f"❌ Emotion statistics failed: {e}")
            return {'error': str(e)}
            
    def create_voice_style_ssml(self, text: str, style: str = 'neutral') -> str:
        """Create SSML with specific voice style"""
        try:
            style_templates = {
                'cheerful': '<mstts:express-as style="cheerful">{text}</mstts:express-as>',
                'sad': '<mstts:express-as style="sad">{text}</mstts:express-as>',
                'angry': '<mstts:express-as style="angry">{text}</mstts:express-as>',
                'fearful': '<mstts:express-as style="fearful">{text}</mstts:express-as>',
                'excited': '<mstts:express-as style="excited">{text}</mstts:express-as>',
                'friendly': '<mstts:express-as style="friendly">{text}</mstts:express-as>',
                'hopeful': '<mstts:express-as style="hopeful">{text}</mstts:express-as>',
                'shouting': '<mstts:express-as style="shouting">{text}</mstts:express-as>',
                'terrified': '<mstts:express-as style="terrified">{text}</mstts:express-as>',
                'unfriendly': '<mstts:express-as style="unfriendly">{text}</mstts:express-as>',
                'whispering': '<mstts:express-as style="whispering">{text}</mstts:express-as>'
            }
            
            if style in style_templates:
                styled_text = style_templates[style].format(text=text)
                
                # Wrap in complete SSML document
                ssml_doc = f'''<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" 
                               xmlns:mstts="https://www.w3.org/2001/mstts" xml:lang="en-US">
    <voice name="en-US-AriaNeural">
        {styled_text}
    </voice>
</speak>'''
                
                return ssml_doc
            else:
                return self.create_ssml_document(text)
                
        except Exception as e:
            print(f"❌ Voice style SSML creation failed: {e}")
            return f'<speak>{text}</speak>'
            
    def get_system_status(self) -> Dict:
        """Get emotion and SSML system status"""
        return {
            'emotion_detection_available': self.emotion_available,
            'ssml_templates_loaded': len(self.ssml_templates),
            'emotion_mappings_loaded': len(self.emotion_voice_mapping),
            'supported_emotions': list(self.emotion_voice_mapping.keys()),
            'supported_styles': ['cheerful', 'sad', 'angry', 'fearful', 'excited', 'friendly'],
            'prosody_settings': self.prosody_settings
        }
