#!/usr/bin/env python3
"""
AUDIO TEST - Find out what's broken
"""

import pyaudio
import numpy as np
import time
import subprocess

def test_microphone():
    """Test if microphone is working"""
    print("🎤 TESTING MICROPHONE...")
    
    try:
        audio = pyaudio.PyAudio()
        
        # List audio devices
        print("\n📱 AUDIO DEVICES:")
        for i in range(audio.get_device_count()):
            info = audio.get_device_info_by_index(i)
            if info['maxInputChannels'] > 0:
                print(f"   {i}: {info['name']} (INPUT)")
                
        # Test recording
        stream = audio.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=16000,
            input=True,
            frames_per_buffer=1600
        )
        
        print("\n🎤 SPEAK NOW FOR 3 SECONDS...")
        
        max_volume = 0
        for i in range(30):  # 3 seconds
            data = stream.read(1600, exception_on_overflow=False)
            audio_array = np.frombuffer(data, dtype=np.int16)
            volume = np.sqrt(np.mean(audio_array**2))
            max_volume = max(max_volume, volume)
            
            # Show volume bar
            bar = "█" * int(volume / 100)
            print(f"\r🔊 Volume: {volume:4.0f} {bar}                    ", end="")
            time.sleep(0.1)
            
        stream.stop_stream()
        stream.close()
        audio.terminate()
        
        print(f"\n✅ Max volume detected: {max_volume}")
        
        if max_volume > 500:
            print("✅ MICROPHONE IS WORKING!")
            return True
        else:
            print("❌ MICROPHONE TOO QUIET OR NOT WORKING!")
            return False
            
    except Exception as e:
        print(f"❌ MICROPHONE ERROR: {e}")
        return False

def test_speakers():
    """Test if speakers are working"""
    print("\n🔊 TESTING SPEAKERS...")
    
    try:
        # Test Windows TTS (should always work)
        print("🎵 Playing test sound with Windows TTS...")
        
        command = 'powershell -Command "Add-Type -AssemblyName System.Speech; $synth = New-Object System.Speech.Synthesis.SpeechSynthesizer; $synth.Speak(\'Testing speakers. Can you hear this?\')"'
        
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ SPEAKERS ARE WORKING!")
            return True
        else:
            print(f"❌ SPEAKER ERROR: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ SPEAKER ERROR: {e}")
        return False

def test_whisper():
    """Test Whisper with a simple audio file"""
    print("\n🧠 TESTING WHISPER...")
    
    try:
        import whisper
        
        # Create a simple test
        print("🎤 SPEAK 'HELLO' FOR 2 SECONDS...")
        
        audio = pyaudio.PyAudio()
        stream = audio.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=16000,
            input=True,
            frames_per_buffer=1600
        )
        
        frames = []
        for i in range(32):  # 2 seconds
            data = stream.read(1600, exception_on_overflow=False)
            frames.append(data)
            
        stream.stop_stream()
        stream.close()
        audio.terminate()
        
        # Convert to audio array
        audio_data = b''.join(frames)
        audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
        
        # Test Whisper
        print("🧠 Processing with Whisper...")
        model = whisper.load_model("base")  # Use small model for test
        result = model.transcribe(audio_array, language="en")
        text = result["text"].strip()
        
        print(f"✅ WHISPER HEARD: '{text}'")
        
        if text and len(text) > 0:
            print("✅ WHISPER IS WORKING!")
            return True
        else:
            print("❌ WHISPER NOT DETECTING SPEECH!")
            return False
            
    except Exception as e:
        print(f"❌ WHISPER ERROR: {e}")
        return False

def main():
    print("🔧 AUDIO SYSTEM DIAGNOSTIC")
    print("=" * 40)
    
    # Test each component
    mic_ok = test_microphone()
    speaker_ok = test_speakers()
    whisper_ok = test_whisper()
    
    print("\n📊 DIAGNOSTIC RESULTS:")
    print("=" * 40)
    print(f"🎤 Microphone: {'✅ WORKING' if mic_ok else '❌ BROKEN'}")
    print(f"🔊 Speakers: {'✅ WORKING' if speaker_ok else '❌ BROKEN'}")
    print(f"🧠 Whisper: {'✅ WORKING' if whisper_ok else '❌ BROKEN'}")
    
    if mic_ok and speaker_ok and whisper_ok:
        print("\n🎉 ALL SYSTEMS WORKING!")
        print("The voice AI should work now.")
    else:
        print("\n🚨 PROBLEMS FOUND:")
        if not mic_ok:
            print("   - Fix microphone (check volume, permissions)")
        if not speaker_ok:
            print("   - Fix speakers (check volume, audio drivers)")
        if not whisper_ok:
            print("   - Fix Whisper (check CUDA, model loading)")

if __name__ == "__main__":
    main()
