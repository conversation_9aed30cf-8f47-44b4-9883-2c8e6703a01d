#!/usr/bin/env python3
"""
RESEARCH ORCHESTRATOR
Chain of Thought: Task Planning → Resource Allocation → Parallel Execution → Result Synthesis
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

class TaskType(Enum):
    SIMPLE_QUERY = "simple_query"
    RESEARCH_PAPER = "research_paper"
    CODE_ANALYSIS = "code_analysis"
    MULTI_MODAL = "multi_modal"
    CROSS_SYSTEM = "cross_system"

@dataclass
class ResearchTask:
    """Research task definition"""
    task_id: str
    task_type: TaskType
    query: str
    requirements: Dict[str, Any]
    priority: int = 1
    estimated_time: int = 30  # seconds
    
class ResearchOrchestrator:
    """Orchestrates complex research workflows across systems and models"""
    
    def __init__(self, model_router, system_coordinator):
        self.logger = logging.getLogger(__name__)
        self.model_router = model_router
        self.system_coordinator = system_coordinator
        
        # Task management
        self.active_tasks = {}
        self.task_history = []
        self.research_context = {}
        
    async def initialize(self):
        """Initialize the research orchestrator"""
        self.logger.info("Initializing Research Orchestrator")
        
        # Verify dependencies
        if not self.model_router or not self.system_coordinator:
            raise ValueError("Model router and system coordinator required")
        
        self.logger.info("Research orchestrator ready for complex workflows")
    
    async def process_research_request(self, query: str, context: Dict = None) -> Dict:
        """
        Main entry point for research requests
        Chain of Thought: Analyze → Plan → Execute → Synthesize
        """
        
        # Step 1: Analyze the request
        task_analysis = self._analyze_research_request(query, context)
        
        # Step 2: Plan the execution strategy
        execution_plan = self._create_execution_plan(task_analysis)
        
        # Step 3: Execute the plan
        results = await self._execute_research_plan(execution_plan)
        
        # Step 4: Synthesize final response
        final_response = await self._synthesize_results(query, results, execution_plan)
        
        # Track the research session
        self._track_research_session(query, execution_plan, results, final_response)
        
        return final_response
    
    def _analyze_research_request(self, query: str, context: Dict = None) -> Dict:
        """Analyze the research request to determine complexity and requirements"""
        
        query_lower = query.lower()
        
        analysis = {
            "complexity": "low",
            "required_systems": [],
            "required_models": [],
            "task_type": TaskType.SIMPLE_QUERY,
            "estimated_steps": 1,
            "requires_multimodal": False,
            "requires_reasoning": False,
            "requires_research": False,
            "requires_file_ops": False
        }
        
        # Detect complexity indicators
        high_complexity_keywords = [
            "analyze and compare", "comprehensive research", "detailed analysis",
            "write a report", "investigate thoroughly", "create a summary",
            "find patterns", "synthesize information", "cross-reference"
        ]
        
        if any(keyword in query_lower for keyword in high_complexity_keywords):
            analysis["complexity"] = "high"
            analysis["estimated_steps"] = 3
        
        # Detect required capabilities
        if any(word in query_lower for word in ["image", "photo", "diagram", "chart", "visual"]):
            analysis["requires_multimodal"] = True
            analysis["required_models"].append("qwen2.5vl:32b")
            analysis["task_type"] = TaskType.MULTI_MODAL
        
        if any(word in query_lower for word in ["search", "find", "research", "latest", "current"]):
            analysis["requires_research"] = True
            analysis["required_systems"].append("agentic_seek")
            analysis["task_type"] = TaskType.RESEARCH_PAPER
        
        if any(word in query_lower for word in ["analyze", "reason", "solve", "logic", "explain why"]):
            analysis["requires_reasoning"] = True
            analysis["required_models"].append("deepseek-r1:14b")
        
        if any(word in query_lower for word in ["save", "write file", "create file", "store"]):
            analysis["requires_file_ops"] = True
            analysis["required_systems"].append("agent_zero")
        
        if any(word in query_lower for word in ["code", "programming", "debug", "script"]):
            analysis["task_type"] = TaskType.CODE_ANALYSIS
            analysis["required_models"].append("phi4-reasoning:plus")
        
        # Cross-system detection
        if len(analysis["required_systems"]) > 1:
            analysis["task_type"] = TaskType.CROSS_SYSTEM
            analysis["estimated_steps"] = len(analysis["required_systems"])
        
        return analysis
    
    def _create_execution_plan(self, analysis: Dict) -> Dict:
        """Create detailed execution plan based on analysis"""
        
        plan = {
            "steps": [],
            "parallel_tasks": [],
            "estimated_time": 0,
            "resource_requirements": {
                "models": analysis["required_models"],
                "systems": analysis["required_systems"]
            }
        }
        
        # Build execution steps based on task type
        if analysis["task_type"] == TaskType.SIMPLE_QUERY:
            plan["steps"] = [
                {
                    "step": 1,
                    "action": "llm_query",
                    "model": "llama3.2:latest",  # Fast response
                    "estimated_time": 5
                }
            ]
        
        elif analysis["task_type"] == TaskType.RESEARCH_PAPER:
            plan["steps"] = [
                {
                    "step": 1,
                    "action": "web_search",
                    "system": "agentic_seek",
                    "estimated_time": 10
                },
                {
                    "step": 2,
                    "action": "analyze_results",
                    "model": "qwen3:32b",
                    "estimated_time": 15
                },
                {
                    "step": 3,
                    "action": "synthesize_response",
                    "model": "deepseek-r1:14b",
                    "estimated_time": 10
                }
            ]
        
        elif analysis["task_type"] == TaskType.MULTI_MODAL:
            plan["steps"] = [
                {
                    "step": 1,
                    "action": "multimodal_analysis",
                    "model": "qwen2.5vl:32b",
                    "estimated_time": 20
                }
            ]
        
        elif analysis["task_type"] == TaskType.CODE_ANALYSIS:
            plan["steps"] = [
                {
                    "step": 1,
                    "action": "code_analysis",
                    "model": "phi4-reasoning:plus",
                    "estimated_time": 15
                }
            ]
        
        elif analysis["task_type"] == TaskType.CROSS_SYSTEM:
            # Complex multi-step workflow
            plan["steps"] = [
                {
                    "step": 1,
                    "action": "parallel_search",
                    "parallel": True,
                    "subtasks": [
                        {"action": "web_search", "system": "agentic_seek"},
                        {"action": "knowledge_search", "system": "agent_zero"}
                    ],
                    "estimated_time": 15
                },
                {
                    "step": 2,
                    "action": "cross_reference_analysis",
                    "model": "deepseek-r1:14b",
                    "estimated_time": 20
                },
                {
                    "step": 3,
                    "action": "save_results",
                    "system": "agent_zero",
                    "estimated_time": 5
                }
            ]
        
        # Calculate total estimated time
        plan["estimated_time"] = sum(step.get("estimated_time", 10) for step in plan["steps"])
        
        return plan
    
    async def _execute_research_plan(self, plan: Dict) -> Dict:
        """Execute the research plan step by step"""
        
        results = {
            "steps_completed": 0,
            "step_results": [],
            "total_time": 0,
            "success": True,
            "error": None
        }
        
        start_time = time.time()
        
        try:
            for step in plan["steps"]:
                step_start = time.time()
                self.logger.info(f"Executing step {step.get('step', '?')}: {step.get('action')}")
                
                step_result = await self._execute_step(step)
                
                step_duration = time.time() - step_start
                step_result["duration"] = step_duration
                
                results["step_results"].append(step_result)
                results["steps_completed"] += 1
                
                # Check for step failure
                if not step_result.get("success", False):
                    self.logger.warning(f"Step {step.get('step')} failed: {step_result.get('error')}")
                    # Continue with other steps rather than failing completely
        
        except Exception as e:
            self.logger.error(f"Plan execution failed: {e}")
            results["success"] = False
            results["error"] = str(e)
        
        results["total_time"] = time.time() - start_time
        return results
    
    async def _execute_step(self, step: Dict) -> Dict:
        """Execute a single step in the research plan"""
        
        action = step.get("action")
        
        try:
            if action == "llm_query":
                model = step.get("model", "llama3.2:latest")
                query = step.get("query", "")
                model_name, response = await self.model_router.route_query(query)
                return {
                    "success": True,
                    "action": action,
                    "model_used": model_name,
                    "result": response
                }
            
            elif action == "web_search":
                query = step.get("query", "")
                result = await self.system_coordinator.execute_web_search(query)
                return {
                    "success": result.get("success", False),
                    "action": action,
                    "result": result.get("result"),
                    "error": result.get("error")
                }
            
            elif action == "knowledge_search":
                query = step.get("query", "")
                result = await self.system_coordinator.search_knowledge_base(query)
                return {
                    "success": result.get("success", False),
                    "action": action,
                    "result": result.get("result"),
                    "error": result.get("error")
                }
            
            elif action == "analyze_results":
                model = step.get("model", "qwen3:32b")
                previous_results = step.get("context", "")
                model_name, response = await self.model_router.route_query(
                    f"Analyze these research results: {previous_results}"
                )
                return {
                    "success": True,
                    "action": action,
                    "model_used": model_name,
                    "result": response
                }
            
            elif action == "parallel_search":
                # Execute multiple searches in parallel
                subtasks = step.get("subtasks", [])
                parallel_results = await asyncio.gather(*[
                    self._execute_step(subtask) for subtask in subtasks
                ], return_exceptions=True)
                
                return {
                    "success": True,
                    "action": action,
                    "result": parallel_results
                }
            
            else:
                return {
                    "success": False,
                    "action": action,
                    "error": f"Unknown action: {action}"
                }
        
        except Exception as e:
            return {
                "success": False,
                "action": action,
                "error": str(e)
            }
    
    async def _synthesize_results(self, original_query: str, results: Dict, plan: Dict) -> Dict:
        """Synthesize all results into a final coherent response"""
        
        # Collect all successful results
        successful_results = []
        for step_result in results.get("step_results", []):
            if step_result.get("success") and step_result.get("result"):
                successful_results.append(step_result["result"])
        
        if not successful_results:
            return {
                "success": False,
                "response": "I wasn't able to find any results for your query.",
                "details": results
            }
        
        # Use reasoning model to synthesize final response
        synthesis_prompt = f"""
        Original query: {original_query}
        
        Research results from multiple sources:
        {chr(10).join([str(result) for result in successful_results])}
        
        Please provide a comprehensive, well-structured response that addresses the original query using the research results.
        Be clear, accurate, and cite the different sources of information when relevant.
        """
        
        try:
            model_name, synthesized_response = await self.model_router.route_query(
                synthesis_prompt,
                context={"priority": "quality"}
            )
            
            return {
                "success": True,
                "response": synthesized_response,
                "model_used": model_name,
                "execution_time": results.get("total_time", 0),
                "steps_completed": results.get("steps_completed", 0),
                "details": {
                    "plan": plan,
                    "raw_results": results
                }
            }
        
        except Exception as e:
            # Fallback to simple concatenation
            return {
                "success": True,
                "response": f"Here's what I found:\n\n" + "\n\n".join([str(r) for r in successful_results]),
                "execution_time": results.get("total_time", 0),
                "steps_completed": results.get("steps_completed", 0),
                "fallback": True,
                "error": str(e)
            }
    
    def _track_research_session(self, query: str, plan: Dict, results: Dict, response: Dict):
        """Track research session for analytics and improvement"""
        
        session = {
            "timestamp": time.time(),
            "query": query[:200],  # First 200 chars
            "plan_complexity": len(plan.get("steps", [])),
            "execution_time": results.get("total_time", 0),
            "success": response.get("success", False),
            "steps_completed": results.get("steps_completed", 0),
            "models_used": [],
            "systems_used": []
        }
        
        # Extract models and systems used
        for step_result in results.get("step_results", []):
            if "model_used" in step_result:
                session["models_used"].append(step_result["model_used"])
            if "action" in step_result and step_result["action"] in ["web_search", "knowledge_search"]:
                session["systems_used"].append(step_result["action"])
        
        self.task_history.append(session)
        
        # Keep only last 100 sessions
        if len(self.task_history) > 100:
            self.task_history = self.task_history[-100:]
    
    async def get_research_analytics(self) -> Dict:
        """Get analytics about research performance"""
        
        if not self.task_history:
            return {"message": "No research sessions recorded yet"}
        
        total_sessions = len(self.task_history)
        successful_sessions = sum(1 for s in self.task_history if s["success"])
        average_time = sum(s["execution_time"] for s in self.task_history) / total_sessions
        
        most_used_models = {}
        for session in self.task_history:
            for model in session["models_used"]:
                most_used_models[model] = most_used_models.get(model, 0) + 1
        
        return {
            "total_sessions": total_sessions,
            "success_rate": f"{(successful_sessions/total_sessions)*100:.1f}%",
            "average_execution_time": f"{average_time:.1f}s",
            "most_used_models": most_used_models,
            "recent_sessions": self.task_history[-5:]  # Last 5 sessions
        }
    
    async def cleanup(self):
        """Cleanup research orchestrator"""
        self.logger.info("Research orchestrator cleanup complete") 