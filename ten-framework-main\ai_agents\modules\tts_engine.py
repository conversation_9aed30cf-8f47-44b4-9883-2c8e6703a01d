#!/usr/bin/env python3
"""
TTS Engine Module - Clean ChatterboxTTS Implementation
Handles all text-to-speech functionality
"""

import os
import time
import torch
import tempfile
import subprocess
import pygame
import warnings
warnings.filterwarnings("ignore")

try:
    import torchaudio
    from chatterbox.tts import ChatterboxTTS
    CHATTERBOX_AVAILABLE = True
except ImportError:
    CHATTERBOX_AVAILABLE = False

class TTSEngine:
    """Clean TTS Engine with ChatterboxTTS"""
    
    def __init__(self, config):
        self.config = config
        self.is_speaking = False
        self.should_interrupt = False
        
        # Initialize TTS
        self._init_tts()
        self._init_audio()
        
    def _init_tts(self):
        """Initialize TTS engine"""
        print("🔄 Initializing TTS Engine...")
        
        if self.config.use_chatterbox and CHATTERBOX_AVAILABLE:
            try:
                print(f"📱 Loading ChatterboxTTS...")
                self.chatterbox_tts = ChatterboxTTS.from_pretrained(
                    device=self.config.device
                )
                print(f"✅ ChatterboxTTS ready on {self.config.device}")
                self.tts_mode = "chatterbox"
                return
            except Exception as e:
                print(f"❌ ChatterboxTTS failed: {e}")
        
        print("🔄 Using Windows TTS fallback")
        self.tts_mode = "windows"
        self.chatterbox_tts = None
        
    def _init_audio(self):
        """Initialize audio playback"""
        try:
            pygame.mixer.init(
                frequency=self.config.output_sample_rate,
                size=-16,
                channels=2,
                buffer=512
            )
            print("✅ Audio playback ready")
        except Exception as e:
            print(f"⚠️ Audio warning: {e}")
            
    def speak(self, text: str):
        """Main speak method"""
        if not text.strip():
            return
            
        print(f"🗣️ Speaking: {text[:50]}...")
        
        self.is_speaking = True
        self.should_interrupt = False
        
        try:
            if self.tts_mode == "chatterbox" and self.chatterbox_tts:
                self._speak_chatterbox(text)
            else:
                self._speak_windows(text)
        except Exception as e:
            print(f"❌ TTS Error: {e}")
            self._speak_windows_basic(text)
        finally:
            self.is_speaking = False
            
    def _speak_chatterbox(self, text: str):
        """ChatterboxTTS speech"""
        try:
            with torch.no_grad():
                # Generate audio
                audio_tensor = self.chatterbox_tts.generate(text)
                
                # Convert to numpy
                if isinstance(audio_tensor, torch.Tensor):
                    audio_np = audio_tensor.cpu().numpy()
                else:
                    audio_np = audio_tensor
                    
                if len(audio_np.shape) > 1:
                    audio_np = audio_np.squeeze()
                    
                # Save and play
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                    torchaudio.save(
                        tmp_file.name,
                        torch.from_numpy(audio_np).unsqueeze(0),
                        self.config.output_sample_rate
                    )
                    self._play_audio_file(tmp_file.name)
                    
        except Exception as e:
            print(f"❌ ChatterboxTTS error: {e}")
            raise
            
    def _speak_windows(self, text: str):
        """Enhanced Windows TTS"""
        try:
            clean_text = text.replace('"', '').replace("'", "")
            clean_text = ' '.join(clean_text.split())
            
            ps_cmd = f'''
            Add-Type -AssemblyName System.Speech;
            $synth = New-Object System.Speech.Synthesis.SpeechSynthesizer;
            $synth.Rate = 1;
            $synth.Volume = 100;
            $synth.Speak("{clean_text}");
            '''
            
            process = subprocess.Popen(
                ['powershell', '-Command', ps_cmd],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            # Monitor for interruption
            while process.poll() is None:
                if self.should_interrupt:
                    process.terminate()
                    print("🛑 Speech interrupted!")
                    return
                time.sleep(0.05)
                
        except Exception as e:
            print(f"❌ Windows TTS error: {e}")
            raise
            
    def _speak_windows_basic(self, text: str):
        """Basic Windows TTS fallback"""
        try:
            clean_text = text.replace('"', '').replace("'", "")
            ps_cmd = f'Add-Type -AssemblyName System.Speech; (New-Object System.Speech.Synthesis.SpeechSynthesizer).Speak("{clean_text}")'
            subprocess.run(
                ['powershell', '-Command', ps_cmd],
                check=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
        except Exception as e:
            print(f"❌ Basic TTS error: {e}")

    def _play_audio_file(self, audio_file: str):
        """Play audio file with interruption support"""
        try:
            pygame.mixer.music.load(audio_file)
            pygame.mixer.music.play()

            while pygame.mixer.music.get_busy():
                if self.should_interrupt:
                    pygame.mixer.music.stop()
                    print("🛑 Audio interrupted!")
                    return
                time.sleep(0.05)

        except Exception as e:
            print(f"❌ Audio playback error: {e}")
        finally:
            try:
                os.unlink(audio_file)
            except:
                pass

    def interrupt(self):
        """Interrupt current speech"""
        self.should_interrupt = True

    def is_busy(self) -> bool:
        """Check if TTS is currently speaking"""
        return self.is_speaking
