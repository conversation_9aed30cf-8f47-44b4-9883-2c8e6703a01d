#!/usr/bin/env python3
"""
Test script to verify the circular dependency fix in tools.py
"""
import os
import sys

print("Starting test...")

# Set work directory environment variable
os.environ['WORK_DIR'] = os.path.join(os.getcwd(), "work_dir")
print(f"Set WORK_DIR to: {os.environ['WORK_DIR']}")

# Add sources to path
sys.path.append('sources')
print("Added sources to path")

print("About to import Tools...")
try:
    from tools.tools import Tools
    print("✓ Successfully imported Tools class")

    print("About to create Tools instance...")
    # Test creating Tools instance
    tools = Tools()
    print("✓ Successfully created Tools instance")

    print("About to test get_work_dir...")
    # Test getting work directory
    work_dir = tools.get_work_dir()
    print(f"✓ Work directory: {work_dir}")

    print("About to test safe_get_work_dir_path...")
    # Test safe_get_work_dir_path
    safe_path = tools.safe_get_work_dir_path()
    print(f"✓ Safe work directory path: {safe_path}")

    print("\n🎉 All tests passed! Circular dependency is fixed.")

except RecursionError as e:
    print(f"❌ Circular dependency still exists: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
except Exception as e:
    print(f"❌ Other error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
