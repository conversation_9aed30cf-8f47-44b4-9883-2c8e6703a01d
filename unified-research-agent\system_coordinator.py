#!/usr/bin/env python3
"""
SYSTEM COORDINATOR
Chain of Thought: Discover → Connect → Monitor → Route Commands
"""

import asyncio
import logging
import sys
import importlib
import importlib.util
import requests
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any

class SystemCoordinator:
    """Coordinates all existing systems for unified operation"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.systems = {}
        self.system_status = {}
        
        # Base paths to existing systems
        self.base_dir = Path(__file__).parent.parent
        self.system_paths = {
            "agent_zero": self.base_dir / "agent-zero-main",
            "agentic_seek": self.base_dir / "agenticSeek-main",
            "livekit_agents": self.base_dir / "agents-main"
        }
        
    async def initialize(self):
        """Initialize connections to all systems"""
        self.logger.info("Initializing System Coordinator")
        
        # Initialize each system
        await self._connect_agent_zero()
        await self._connect_agentic_seek()
        await self._connect_livekit()
        
        # Report system status
        self._report_system_status()
        
    async def _connect_agent_zero(self):
        """Connect to Agent-Zero system"""
        try:
            agent_zero_path = self.system_paths["agent_zero"]
            
            if not agent_zero_path.exists():
                self.logger.warning("Agent-Zero path not found")
                self.systems["agent_zero"] = {"status": "not_found"}
                return
            
            # Add to Python path
            sys.path.insert(0, str(agent_zero_path))
            
            # Import Agent-Zero modules
            from python.helpers import files, errors, files_manager
            from python.tools import browser_agent, knowledge_search, code_editor
            
            # Load agent configuration
            agent_config = None
            if (agent_zero_path / "agent.py").exists():
                spec = importlib.util.spec_from_file_location("agent", agent_zero_path / "agent.py")
                agent_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(agent_module)
                agent_config = agent_module
            
            self.systems["agent_zero"] = {
                "status": "connected",
                "capabilities": [
                    "file_operations", 
                    "browser_automation", 
                    "knowledge_management",
                    "code_editing",
                    "memory_persistence"
                ],
                "modules": {
                    "files": files,
                    "errors": errors,
                    "files_manager": files_manager,
                    "browser_agent": browser_agent,
                    "knowledge_search": knowledge_search,
                    "code_editor": code_editor
                },
                "config": agent_config,
                "memory_path": agent_zero_path / "memory",
                "knowledge_path": agent_zero_path / "knowledge"
            }
            
            self.logger.info("Agent-Zero system connected")
            
        except Exception as e:
            self.logger.error(f"Failed to connect to Agent-Zero: {e}")
            self.systems["agent_zero"] = {"status": "error", "error": str(e)}
    
    async def _connect_agentic_seek(self):
        """Connect to AgenticSeek system"""
        try:
            agentic_seek_path = self.system_paths["agentic_seek"]
            
            if not agentic_seek_path.exists():
                self.logger.warning("AgenticSeek path not found")
                self.systems["agentic_seek"] = {"status": "not_found"}
                return
            
            # Add to Python path
            sys.path.insert(0, str(agentic_seek_path))
            
            # Import AgenticSeek modules
            from sources.agents import Agent, BrowserAgent, PlannerAgent
            from sources.tools import PyInterpreter, BashInterpreter, FileFinder
            
            # Check if API server is running
            api_status = await self._check_agentic_seek_api()
            
            self.systems["agentic_seek"] = {
                "status": "connected",
                "api_status": api_status,
                "capabilities": [
                    "web_search",
                    "research_automation", 
                    "browser_automation",
                    "data_scraping",
                    "search_aggregation"
                ],
                "modules": {
                    "Agent": Agent,
                    "BrowserAgent": BrowserAgent,
                    "PlannerAgent": PlannerAgent,
                    "PyInterpreter": PyInterpreter,
                    "BashInterpreter": BashInterpreter,
                    "FileFinder": FileFinder
                },
                "work_dir": agentic_seek_path / "work_dir"
            }
            
            self.logger.info("AgenticSeek system connected")
            
        except Exception as e:
            self.logger.error(f"Failed to connect to AgenticSeek: {e}")
            self.systems["agentic_seek"] = {"status": "error", "error": str(e)}
    
    async def _check_agentic_seek_api(self) -> str:
        """Check if AgenticSeek API is running"""
        try:
            response = requests.get("http://localhost:7777/health", timeout=3)
            return "running" if response.status_code == 200 else "stopped"
        except:
            return "stopped"
    
    async def _connect_livekit(self):
        """Connect to LiveKit voice system"""
        try:
            livekit_path = self.system_paths["livekit_agents"]
            
            if not livekit_path.exists():
                self.logger.warning("LiveKit path not found")
                self.systems["livekit"] = {"status": "not_found"}
                return
            
            # Add to Python path
            sys.path.insert(0, str(livekit_path))
            
            # Import LiveKit modules
            from livekit.agents import Agent, JobContext, JobProcess
            from livekit.plugins import openai, deepgram, silero
            
            self.systems["livekit"] = {
                "status": "connected",
                "capabilities": [
                    "voice_conversation",
                    "real_time_audio",
                    "professional_tts",
                    "advanced_stt",
                    "webrtc_integration"
                ],
                "modules": {
                    "Agent": Agent,
                    "JobContext": JobContext,
                    "JobProcess": JobProcess,
                    "openai": openai,
                    "deepgram": deepgram,
                    "silero": silero
                },
                "examples_path": livekit_path / "examples" / "voice_agents"
            }
            
            self.logger.info("LiveKit system connected")
            
        except Exception as e:
            self.logger.error(f"Failed to connect to LiveKit: {e}")
            self.systems["livekit"] = {"status": "error", "error": str(e)}
    
    def _report_system_status(self):
        """Report the status of all systems"""
        self.logger.info("System Connection Status:")
        for system_name, system_info in self.systems.items():
            status = system_info.get("status", "unknown")
            capabilities = system_info.get("capabilities", [])
            
            if status == "connected":
                self.logger.info(f"  {system_name}: CONNECTED - {len(capabilities)} capabilities")
            elif status == "error":
                self.logger.error(f"  {system_name}: ERROR - {system_info.get('error', 'unknown')}")
            else:
                self.logger.warning(f"  {system_name}: {status.upper()}")
    
    # AGENT-ZERO OPERATIONS
    async def execute_file_operation(self, operation: str, **kwargs) -> Dict:
        """Execute file operations through Agent-Zero"""
        if self.systems.get("agent_zero", {}).get("status") != "connected":
            return {"error": "Agent-Zero not connected"}
        
        try:
            files_module = self.systems["agent_zero"]["modules"]["files"]
            
            if operation == "read":
                result = files_module.read_file(kwargs.get("filepath"))
            elif operation == "write":
                result = files_module.write_file(kwargs.get("filepath"), kwargs.get("content"))
            elif operation == "list":
                result = files_module.list_directory(kwargs.get("directory", "."))
            else:
                return {"error": f"Unknown file operation: {operation}"}
            
            return {"success": True, "result": result}
            
        except Exception as e:
            return {"error": f"File operation failed: {e}"}
    
    async def search_knowledge_base(self, query: str) -> Dict:
        """Search Agent-Zero knowledge base"""
        if self.systems.get("agent_zero", {}).get("status") != "connected":
            return {"error": "Agent-Zero not connected"}
        
        try:
            knowledge_module = self.systems["agent_zero"]["modules"]["knowledge_search"]
            result = knowledge_module.search(query)
            return {"success": True, "result": result}
        except Exception as e:
            return {"error": f"Knowledge search failed: {e}"}
    
    # AGENTICSEK OPERATIONS
    async def execute_web_search(self, query: str, num_results: int = 5) -> Dict:
        """Execute web search through AgenticSeek"""
        if self.systems.get("agentic_seek", {}).get("status") != "connected":
            return {"error": "AgenticSeek not connected"}
        
        try:
            browser_agent = self.systems["agentic_seek"]["modules"]["BrowserAgent"]
            # For now, return a placeholder since we need to implement proper search
            result = f"Search query: {query} (AgenticSeek integration needs specific implementation)"
            return {"success": True, "result": result}
        except Exception as e:
            return {"error": f"Web search failed: {e}"}
    
    async def execute_research_task(self, topic: str, depth: str = "medium") -> Dict:
        """Execute research task through AgenticSeek"""
        if self.systems.get("agentic_seek", {}).get("status") != "connected":
            return {"error": "AgenticSeek not connected"}
        
        try:
            planner_agent = self.systems["agentic_seek"]["modules"]["PlannerAgent"]
            # For now, return a placeholder since we need to implement proper research
            result = f"Research topic: {topic} with depth: {depth} (AgenticSeek integration needs specific implementation)"
            return {"success": True, "result": result}
        except Exception as e:
            return {"error": f"Research task failed: {e}"}
    
    # SYSTEM STATUS AND HEALTH
    async def get_system_health(self) -> Dict:
        """Get health status of all systems"""
        health_status = {}
        
        for system_name, system_info in self.systems.items():
            health_status[system_name] = {
                "status": system_info.get("status"),
                "capabilities": system_info.get("capabilities", []),
                "last_check": "now"
            }
        
        return health_status
    
    async def execute_cross_system_task(self, task_type: str, **kwargs) -> Dict:
        """Execute tasks that require multiple systems"""
        
        if task_type == "research_and_save":
            # Use AgenticSeek for research, Agent-Zero for saving
            search_result = await self.execute_web_search(kwargs.get("query"))
            if search_result.get("success"):
                file_result = await self.execute_file_operation(
                    "write",
                    filepath=kwargs.get("save_path", "research_output.txt"),
                    content=str(search_result["result"])
                )
                return {"success": True, "search": search_result, "save": file_result}
            else:
                return search_result
        
        elif task_type == "knowledge_enhanced_search":
            # Search both knowledge base and web
            kb_result = await self.search_knowledge_base(kwargs.get("query"))
            web_result = await self.execute_web_search(kwargs.get("query"))
            
            return {
                "success": True,
                "knowledge_base": kb_result,
                "web_search": web_result
            }
        
        else:
            return {"error": f"Unknown cross-system task: {task_type}"}
    
    async def cleanup(self):
        """Cleanup system connections"""
        self.logger.info("System coordinator cleanup complete") 