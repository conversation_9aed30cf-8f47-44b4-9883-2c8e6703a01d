#!/usr/bin/env python3
"""
ULTRA-FAST INTERRUPTION SYSTEM
50ms interruption detection with real-time voice processing
"""

import numpy as np
import pyaudio
import webrtcvad
import threading
import time
from collections import deque
from typing import Callable, Optional, Dict, Any

class UltraFastInterruption:
    """Ultra-fast interruption system with 50ms detection"""
    
    def __init__(self, config):
        self.config = config
        self.is_active = False
        self.is_monitoring = False
        
        # Audio settings optimized for ultra-fast detection
        self.sample_rate = 16000  # WebRTC VAD requirement
        self.frame_duration_ms = self.config.vad_frame_duration_ms  # 10ms frames
        self.frame_size = int(self.sample_rate * self.frame_duration_ms / 1000)
        self.chunk_size = self.frame_size * 2  # Process 2 frames at once
        
        # Interruption settings
        self.interruption_threshold = self.config.interruption_threshold_ms  # 50ms
        self.sensitivity = self.config.interruption_sensitivity  # 0.3
        
        # Audio components
        self.audio = None
        self.stream = None
        self.vad = None
        
        # Monitoring thread
        self.monitor_thread = None
        self.audio_buffer = deque(maxlen=100)  # Small buffer for speed
        
        # Callbacks
        self.on_interruption_detected = None
        self.on_speech_started = None
        self.on_speech_ended = None
        
        self._init_components()
    
    def _init_components(self):
        """Initialize audio and VAD components"""
        try:
            print("⚡ INITIALIZING ULTRA-FAST INTERRUPTION SYSTEM")
            print("=" * 60)
            
            # Initialize PyAudio
            self.audio = pyaudio.PyAudio()
            
            # Find best microphone
            device_index = None
            for i in range(self.audio.get_device_count()):
                info = self.audio.get_device_info_by_index(i)
                if info['maxInputChannels'] > 0:
                    device_index = i
                    print(f"🎤 Using microphone: {info['name']}")
                    break
            
            # Initialize WebRTC VAD with maximum aggressiveness
            self.vad = webrtcvad.Vad(3)  # Most aggressive mode
            
            # Create audio stream with minimal latency
            self.stream = self.audio.open(
                format=pyaudio.paInt16,
                channels=1,
                rate=self.sample_rate,
                input=True,
                input_device_index=device_index,
                frames_per_buffer=self.chunk_size,
                stream_callback=self._audio_callback
            )
            
            print(f"⚡ Frame Duration: {self.frame_duration_ms}ms")
            print(f"⚡ Interruption Threshold: {self.interruption_threshold}ms")
            print(f"⚡ Sensitivity: {self.sensitivity}")
            print("✅ Ultra-Fast Interruption System Ready")
            print("=" * 60)
            
        except Exception as e:
            print(f"❌ Failed to initialize interruption system: {e}")
            raise
    
    def _audio_callback(self, in_data, frame_count, time_info, status):
        """Ultra-fast audio callback for real-time processing"""
        try:
            if self.is_monitoring:
                # Convert audio data
                audio_data = np.frombuffer(in_data, dtype=np.int16)
                
                # Add to buffer for processing
                self.audio_buffer.append(audio_data)
                
                # Process immediately for ultra-fast detection
                self._process_audio_frame(audio_data)
            
            return (None, pyaudio.paContinue)
            
        except Exception as e:
            print(f"❌ Audio Callback Error: {e}")
            return (None, pyaudio.paContinue)
    
    def _process_audio_frame(self, audio_data: np.ndarray):
        """Process audio frame for interruption detection"""
        try:
            # Split into VAD frames (10ms each)
            for i in range(0, len(audio_data), self.frame_size):
                frame = audio_data[i:i + self.frame_size]
                
                if len(frame) == self.frame_size:
                    # Convert to bytes for WebRTC VAD
                    frame_bytes = frame.tobytes()
                    
                    # Check for voice activity
                    is_speech = self.vad.is_speech(frame_bytes, self.sample_rate)
                    
                    if is_speech:
                        self._handle_speech_detected()
                    
        except Exception as e:
            print(f"❌ Frame Processing Error: {e}")
    
    def _handle_speech_detected(self):
        """Handle speech detection for interruption"""
        try:
            current_time = time.time() * 1000  # Convert to milliseconds
            
            if not hasattr(self, '_last_speech_time'):
                self._last_speech_time = current_time
                self._speech_start_time = current_time
                
                # Trigger speech started callback
                if self.on_speech_started:
                    threading.Thread(target=self.on_speech_started, daemon=True).start()
                
                return
            
            # Calculate time since last speech
            time_since_last = current_time - self._last_speech_time
            self._last_speech_time = current_time
            
            # Check for interruption (speech detected during TTS playback)
            if self.is_active and time_since_last < self.interruption_threshold:
                speech_duration = current_time - self._speech_start_time
                
                # Trigger interruption if speech is sustained
                if speech_duration >= self.interruption_threshold:
                    self._trigger_interruption()
            
        except Exception as e:
            print(f"❌ Speech Detection Error: {e}")
    
    def _trigger_interruption(self):
        """Trigger interruption callback"""
        try:
            print("⚡ INTERRUPTION DETECTED!")
            
            if self.on_interruption_detected:
                # Run callback in separate thread for speed
                threading.Thread(target=self.on_interruption_detected, daemon=True).start()
            
            # Reset speech timing
            self._speech_start_time = time.time() * 1000
            
        except Exception as e:
            print(f"❌ Interruption Trigger Error: {e}")
    
    def start_monitoring(self):
        """Start interruption monitoring"""
        try:
            if not self.is_monitoring:
                print("⚡ Starting ultra-fast interruption monitoring...")
                self.is_monitoring = True
                
                # Start audio stream
                if self.stream and not self.stream.is_active():
                    self.stream.start_stream()
                
                print("✅ Interruption monitoring active")
            
        except Exception as e:
            print(f"❌ Failed to start monitoring: {e}")
    
    def stop_monitoring(self):
        """Stop interruption monitoring"""
        try:
            if self.is_monitoring:
                print("⚡ Stopping interruption monitoring...")
                self.is_monitoring = False
                
                # Stop audio stream
                if self.stream and self.stream.is_active():
                    self.stream.stop_stream()
                
                print("✅ Interruption monitoring stopped")
            
        except Exception as e:
            print(f"❌ Failed to stop monitoring: {e}")
    
    def set_active(self, active: bool):
        """Set interruption system active/inactive"""
        self.is_active = active
        if active:
            print("⚡ Interruption system ACTIVE - monitoring for interruptions")
        else:
            print("⚡ Interruption system INACTIVE")
    
    def set_callbacks(self, 
                     on_interruption: Optional[Callable] = None,
                     on_speech_start: Optional[Callable] = None,
                     on_speech_end: Optional[Callable] = None):
        """Set callback functions"""
        self.on_interruption_detected = on_interruption
        self.on_speech_started = on_speech_start
        self.on_speech_ended = on_speech_end
        
        print("⚡ Interruption callbacks configured")
    
    def get_status(self) -> Dict[str, Any]:
        """Get interruption system status"""
        return {
            "is_active": self.is_active,
            "is_monitoring": self.is_monitoring,
            "frame_duration_ms": self.frame_duration_ms,
            "interruption_threshold_ms": self.interruption_threshold,
            "sensitivity": self.sensitivity,
            "buffer_size": len(self.audio_buffer)
        }
    
    def cleanup(self):
        """Clean up resources"""
        try:
            self.stop_monitoring()
            
            if self.stream:
                self.stream.close()
            
            if self.audio:
                self.audio.terminate()
            
            print("✅ Ultra-Fast Interruption System cleaned up")
            
        except Exception as e:
            print(f"❌ Cleanup Error: {e}")
