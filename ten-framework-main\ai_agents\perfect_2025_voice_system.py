#!/usr/bin/env python3
"""
PERFECT 2025 VOICE SYSTEM
Complete voice-to-voice AI with 2025 Hugging Face models and ultra-fast interruption
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

import numpy as np
import time
import threading
from modules.clean_config import PerfectVoiceAIConfig
from modules.perfect_2025_stt import Perfect2025STT
from modules.perfect_2025_tts import Perfect2025TTS
from modules.ultra_fast_interruption import UltraFastInterruption
from modules.fast_ai_client import UltraFastAIClient
from modules.premium_audio_engine import PremiumAudioEngine

class Perfect2025VoiceSystem:
    """Perfect 2025 Voice-to-Voice AI System with all advanced features"""
    
    def __init__(self):
        print("🚀 INITIALIZING PERFECT 2025 VOICE SYSTEM")
        print("=" * 70)
        
        # Load configuration
        self.config = PerfectVoiceAIConfig()
        
        # Initialize components
        self.stt = None
        self.tts = None
        self.interruption = None
        self.ai_client = None
        self.audio_engine = None
        
        # System state
        self.is_running = False
        self.is_listening = False
        self.conversation_active = False
        
        self._init_all_components()
        
    def _init_all_components(self):
        """Initialize all system components"""
        try:
            # Initialize premium audio engine
            print("🎵 Initializing Premium Audio Engine...")
            self.audio_engine = PremiumAudioEngine(self.config)
            
            # Initialize 2025 STT
            print("🎤 Initializing 2025 Speech-to-Text...")
            self.stt = Perfect2025STT(self.config)
            
            # Initialize 2025 TTS
            print("🔊 Initializing 2025 Text-to-Speech...")
            self.tts = Perfect2025TTS(self.config)
            
            # Initialize ultra-fast interruption system
            print("⚡ Initializing Ultra-Fast Interruption System...")
            self.interruption = UltraFastInterruption(self.config)
            
            # Initialize AI client
            print("🤖 Initializing Ultra-Fast AI Client...")
            self.ai_client = UltraFastAIClient(self.config)
            
            # Set up interruption callbacks
            self.interruption.set_callbacks(
                on_interruption=self._handle_interruption,
                on_speech_start=self._handle_speech_start,
                on_speech_end=self._handle_speech_end
            )
            
            print("✅ ALL COMPONENTS INITIALIZED SUCCESSFULLY")
            print("=" * 70)
            
        except Exception as e:
            print(f"❌ Component initialization failed: {e}")
            raise
    
    def start_conversation(self):
        """Start the perfect voice conversation system"""
        try:
            print("🚀 STARTING PERFECT 2025 VOICE CONVERSATION")
            print("=" * 70)
            
            # Display system information
            self._display_system_info()
            
            # Start interruption monitoring
            self.interruption.start_monitoring()
            
            # Set system as running
            self.is_running = True
            self.conversation_active = True
            
            print("🎤 Say something to start the conversation...")
            print("   (Press Ctrl+C to exit)")
            print("=" * 70)
            
            # Main conversation loop
            self._conversation_loop()
            
        except KeyboardInterrupt:
            print("\n⚡ Conversation interrupted by user")
        except Exception as e:
            print(f"❌ Conversation error: {e}")
        finally:
            self._cleanup()
    
    def _conversation_loop(self):
        """Main conversation processing loop"""
        while self.is_running:
            try:
                # Listen for speech
                audio_data = self._listen_for_speech()
                
                if audio_data is not None:
                    # Process the speech
                    self._process_speech_input(audio_data)
                
                # Small delay to prevent CPU overload
                time.sleep(0.01)
                
            except Exception as e:
                print(f"❌ Conversation loop error: {e}")
                time.sleep(0.1)
    
    def _listen_for_speech(self) -> np.ndarray:
        """Listen for speech input with VAD"""
        try:
            # This would integrate with the interruption system's audio capture
            # For now, we'll use a simplified approach
            
            if not self.is_listening:
                self.is_listening = True
                
                # Get audio from interruption system buffer
                if hasattr(self.interruption, 'audio_buffer') and self.interruption.audio_buffer:
                    audio_frames = list(self.interruption.audio_buffer)
                    self.interruption.audio_buffer.clear()
                    
                    if audio_frames:
                        # Combine audio frames
                        audio_data = np.concatenate(audio_frames)
                        
                        # Enhance audio if available
                        if self.audio_engine:
                            audio_data = self.audio_engine.enhance_input_audio(audio_data)
                        
                        return audio_data.astype(np.float32)
            
            return None
            
        except Exception as e:
            print(f"❌ Listen error: {e}")
            return None
        finally:
            self.is_listening = False
    
    def _process_speech_input(self, audio_data: np.ndarray):
        """Process speech input through the complete pipeline"""
        try:
            print("🎤 Processing speech input...")
            
            # Transcribe speech to text
            result = self.stt.transcribe_with_confidence(audio_data)
            text = result.get("text", "").strip()
            confidence = result.get("confidence", 0.0)
            
            if text and confidence > 0.5:
                print(f"🎤 User: '{text}' (confidence: {confidence:.2f})")
                
                # Get AI response
                ai_response = self.ai_client.get_response(text)
                
                if ai_response:
                    print(f"🤖 AI: '{ai_response}'")
                    
                    # Set interruption system active during TTS
                    self.interruption.set_active(True)
                    
                    # Speak the response
                    success = self.tts.speak(ai_response, interrupt_previous=True)
                    
                    if success:
                        print("✅ Response delivered successfully")
                    else:
                        print("❌ Failed to deliver response")
                    
                    # Deactivate interruption system
                    self.interruption.set_active(False)
                else:
                    print("❌ No AI response received")
            else:
                print(f"🔇 Speech not clear enough (confidence: {confidence:.2f})")
                
        except Exception as e:
            print(f"❌ Speech processing error: {e}")
    
    def _handle_interruption(self):
        """Handle interruption detected by the system"""
        try:
            print("⚡ INTERRUPTION DETECTED - Stopping current speech")
            
            # Stop current TTS
            if self.tts:
                self.tts.interrupt_speech()
            
            # Reset conversation state
            self.conversation_active = True
            
            print("✅ Ready for new input")
            
        except Exception as e:
            print(f"❌ Interruption handling error: {e}")
    
    def _handle_speech_start(self):
        """Handle speech start detection"""
        print("🎤 Speech started...")
    
    def _handle_speech_end(self):
        """Handle speech end detection"""
        print("🔇 Speech ended")
    
    def _display_system_info(self):
        """Display system information"""
        try:
            print("📊 SYSTEM INFORMATION")
            print("-" * 40)
            print(f"Device: {self.config.device}")
            print(f"STT Model: {self.config.whisper_model}")
            print(f"TTS Model: {self.config.tts_model}")
            print(f"Interruption Threshold: {self.config.interruption_threshold_ms}ms")
            print(f"Audio Enhancement: {'Enabled' if self.config.audio_enhancement else 'Disabled'}")
            print(f"Parallel Processing: {'Enabled' if self.config.parallel_processing else 'Disabled'}")
            
            # Get component status
            if self.tts:
                tts_info = self.tts.get_voice_info()
                print(f"TTS Engine: {tts_info['primary_tts']}")
            
            if self.interruption:
                int_status = self.interruption.get_status()
                print(f"Interruption System: {'Active' if int_status['is_monitoring'] else 'Inactive'}")
            
            print("-" * 40)
            
        except Exception as e:
            print(f"❌ System info display error: {e}")
    
    def _cleanup(self):
        """Clean up all system resources"""
        try:
            print("\n🧹 CLEANING UP SYSTEM RESOURCES")
            print("=" * 50)
            
            self.is_running = False
            self.conversation_active = False
            
            # Stop interruption monitoring
            if self.interruption:
                self.interruption.stop_monitoring()
                self.interruption.cleanup()
            
            # Stop any ongoing TTS
            if self.tts:
                self.tts.interrupt_speech()
            
            print("✅ System cleanup completed")
            print("=" * 50)
            
        except Exception as e:
            print(f"❌ Cleanup error: {e}")

def main():
    """Main function to run the Perfect 2025 Voice System"""
    try:
        # Create and start the voice system
        voice_system = Perfect2025VoiceSystem()
        voice_system.start_conversation()
        
    except Exception as e:
        print(f"❌ System startup error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
