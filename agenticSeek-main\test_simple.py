#!/usr/bin/env python3
"""
Simple test to check if circular dependency is fixed
"""
import os
import sys

# Add the sources directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'sources'))

print("Testing tools.py circular dependency fix...")

try:
    # Set environment variable
    os.environ['WORK_DIR'] = os.path.join(os.getcwd(), 'work_dir')
    
    # Import the tools module
    from tools.tools import Tools
    
    print("✓ Successfully imported Tools class")
    
    # Try to create an instance (this is where the circular dependency would occur)
    print("Creating Tools instance...")
    
    # Create a simple subclass since Tools is abstract
    class TestTools(Tools):
        def execute(self, blocks, safety):
            return "test"
    
    tools_instance = TestTools()
    print("✓ Successfully created Tools instance")
    print(f"✓ Work directory: {tools_instance.get_work_dir()}")
    
    print("SUCCESS: No circular dependency detected!")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
