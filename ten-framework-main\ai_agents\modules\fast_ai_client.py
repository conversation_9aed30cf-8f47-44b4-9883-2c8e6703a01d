#!/usr/bin/env python3
"""
ULTRA-FAST AI CLIENT
Optimized multi-backend AI integration with connection pooling and fallbacks
"""

import requests
import time
import threading
import queue
from typing import Optional, Dict, Any
from concurrent.futures import ThreadPoolExecutor, TimeoutError
import warnings
warnings.filterwarnings("ignore")

class UltraFastAIClient:
    """Ultra-fast AI client with multiple backends and connection pooling"""

    def __init__(self, config):
        self.config = config
        self.response_cache = {}  # Simple response cache
        self.last_request_time = 0
        self.connection_pool_size = 5
        self.request_queue = queue.Queue()
        self.is_processing = False

        print("🚀 INITIALIZING ULTRA-FAST AI CLIENT")
        print("=" * 50)

        # Initialize connection pools for different backends
        self._init_connection_pools()

        # Initialize AI backends
        self._init_ai_backends()

        # Start background processing thread
        self._start_background_processor()

        print("✅ Ultra-Fast AI Client Ready")
        print("=" * 50)

    def _init_connection_pools(self):
        """Initialize connection pools for different AI backends"""
        # AgenticSeek session with connection pooling
        self.agenticseek_session = requests.Session()
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=self.connection_pool_size,
            pool_maxsize=self.connection_pool_size,
            max_retries=2
        )
        self.agenticseek_session.mount('http://', adapter)
        self.agenticseek_session.mount('https://', adapter)

        # Configure headers for speed
        self.agenticseek_session.headers.update({
            'Content-Type': 'application/json',
            'Connection': 'keep-alive',
            'Accept-Encoding': 'gzip, deflate'
        })

        # Ollama session (local LLM fallback)
        self.ollama_session = requests.Session()
        self.ollama_session.mount('http://', adapter)

        print("✅ Connection pools initialized")

    def _init_ai_backends(self):
        """Initialize and test AI backends"""
        self.available_backends = {}

        # Test AgenticSeek
        if self._test_agenticseek():
            self.available_backends['agenticseek'] = True
            print("✅ AgenticSeek backend ready")
        else:
            self.available_backends['agenticseek'] = False
            print("⚠️ AgenticSeek backend unavailable")

        # Test Ollama (local fallback)
        if self._test_ollama():
            self.available_backends['ollama'] = True
            print("✅ Ollama backend ready")
        else:
            self.available_backends['ollama'] = False
            print("⚠️ Ollama backend unavailable")

        # Simple rule-based fallback
        self.available_backends['simple'] = True
        print("✅ Simple rule-based fallback ready")

    def _test_agenticseek(self) -> bool:
        """Test AgenticSeek connection"""
        try:
            response = self.agenticseek_session.get(
                f"{self.config.agenticseek_url}/health",
                timeout=3
            )
            return response.status_code == 200
        except:
            return False

    def _test_ollama(self) -> bool:
        """Test Ollama connection"""
        try:
            response = self.ollama_session.get(
                "http://localhost:11434/api/tags",
                timeout=2
            )
            return response.status_code == 200
        except:
            return False

    def _start_background_processor(self):
        """Start background thread for processing requests"""
        self.executor = ThreadPoolExecutor(max_workers=3)
        self.is_processing = True

    def get_response(self, text: str) -> Optional[str]:
        """Get AI response with ultra-fast processing and fallbacks"""

        if not text or not text.strip():
            return None

        # Check cache first
        text_hash = hash(text.lower().strip())
        if text_hash in self.response_cache:
            print("⚡ Using cached response")
            return self.response_cache[text_hash]

        print(f"🚀 Ultra-fast AI processing: '{text[:50]}{'...' if len(text) > 50 else ''}'")
        start_time = time.time()

        # Try backends in order of preference
        response = None

        # 1. Try AgenticSeek (primary)
        if self.available_backends.get('agenticseek'):
            response = self._get_agenticseek_response(text)
            if response:
                duration = time.time() - start_time
                print(f"✅ AgenticSeek responded in {duration:.1f}s")
                self._cache_response(text_hash, response)
                return response

        # 2. Try Ollama (local fallback)
        if self.available_backends.get('ollama'):
            response = self._get_ollama_response(text)
            if response:
                duration = time.time() - start_time
                print(f"✅ Ollama responded in {duration:.1f}s")
                self._cache_response(text_hash, response)
                return response

        # 3. Simple rule-based fallback
        response = self._get_simple_response(text)
        duration = time.time() - start_time
        print(f"✅ Simple fallback responded in {duration:.1f}s")
        return response

    def _get_agenticseek_response(self, text: str) -> Optional[str]:
        """Get response from AgenticSeek with timeout"""
        try:
            future = self.executor.submit(self._agenticseek_request, text)
            return future.result(timeout=5)  # 5 second timeout
        except TimeoutError:
            print("⏰ AgenticSeek timeout")
            return None
        except Exception as e:
            print(f"❌ AgenticSeek error: {e}")
            self.available_backends['agenticseek'] = False
            return None

    def _agenticseek_request(self, text: str) -> Optional[str]:
        """Make AgenticSeek request"""
        try:
            response = self.agenticseek_session.post(
                f"{self.config.agenticseek_url}/query",
                json={"query": text},
                timeout=4
            )

            if response.status_code == 200:
                result = response.json()
                return result.get("response", "").strip()
            elif response.status_code == 429:
                time.sleep(0.5)
                return "I'm processing many requests. One moment please."
            else:
                return None

        except Exception:
            return None

    def _get_ollama_response(self, text: str) -> Optional[str]:
        """Get response from Ollama local LLM"""
        try:
            response = self.ollama_session.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": "llama2",  # or whatever model is available
                    "prompt": text,
                    "stream": False
                },
                timeout=8
            )

            if response.status_code == 200:
                result = response.json()
                return result.get("response", "").strip()
            else:
                return None

        except Exception:
            self.available_backends['ollama'] = False
            return None

    def _get_simple_response(self, text: str) -> str:
        """Simple rule-based responses for ultimate fallback"""
        text_lower = text.lower()

        # Greeting responses
        if any(word in text_lower for word in ['hello', 'hi', 'hey', 'greetings']):
            return "Hello! How can I help you today?"

        # Question responses
        if any(word in text_lower for word in ['what', 'how', 'why', 'when', 'where', 'who']):
            return "That's an interesting question. I'm currently running in simple mode, so my responses are limited."

        # Thank you responses
        if any(word in text_lower for word in ['thank', 'thanks']):
            return "You're welcome! Is there anything else I can help you with?"

        # Default response
        return "I understand you're trying to communicate with me. I'm currently in simple response mode."

    def _cache_response(self, text_hash: int, response: str):
        """Cache response for faster future access"""
        if len(self.response_cache) > 50:  # Limit cache size
            # Remove oldest entry
            oldest_key = next(iter(self.response_cache))
            del self.response_cache[oldest_key]

        self.response_cache[text_hash] = response

    def test_ai_response(self) -> bool:
        """Test AI response functionality"""
        print("🧪 Testing ultra-fast AI response...")

        test_query = "Hello, can you hear me?"
        response = self.get_response(test_query)

        if response and len(response) > 0:
            print("✅ Ultra-fast AI response test passed")
            return True
        else:
            print("❌ AI response test failed")
            return False

    def cleanup(self):
        """Cleanup resources"""
        try:
            self.is_processing = False
            if hasattr(self, 'executor'):
                self.executor.shutdown(wait=False)
            if hasattr(self, 'agenticseek_session'):
                self.agenticseek_session.close()
            if hasattr(self, 'ollama_session'):
                self.ollama_session.close()
            print("🧹 Ultra-fast AI Client cleanup completed")
        except Exception as e:
            print(f"⚠️ AI Client cleanup error: {e}")

    def __del__(self):
        """Destructor"""
        try:
            self.cleanup()
        except:
            pass
