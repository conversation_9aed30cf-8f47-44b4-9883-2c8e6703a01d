#!/usr/bin/env python3
"""
FAST AI CLIENT
Optimized AgenticSeek integration for speed
"""

import requests
import time
from typing import Optional
import warnings
warnings.filterwarnings("ignore")

class FastAIClient:
    """Fast, optimized AI client for AgenticSeek"""
    
    def __init__(self, config):
        self.config = config
        self.session = requests.Session()  # Reuse connections
        self.last_request_time = 0
        
        print("🤖 INITIALIZING FAST AI CLIENT")
        print("=" * 40)
        
        # Configure session for speed
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Connection': 'keep-alive'
        })
        
        # Test connection
        if self._test_connection():
            print("✅ Fast AI Client Ready")
        else:
            print("⚠️ AI Client connection issues")
            
        print("=" * 40)
        
    def _test_connection(self) -> bool:
        """Test AgenticSeek connection"""
        try:
            print("🔄 Testing AgenticSeek connection...")
            
            response = self.session.get(
                f"{self.config.agenticseek_url}/health",
                timeout=5
            )
            
            if response.status_code == 200:
                print("✅ AgenticSeek connected")
                return True
            else:
                print(f"⚠️ AgenticSeek returned status: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ AgenticSeek not running - start it first!")
            return False
        except Exception as e:
            print(f"❌ Connection test failed: {e}")
            return False
            
    def get_response(self, text: str) -> Optional[str]:
        """Get AI response quickly"""
        
        if not text or not text.strip():
            return None
            
        try:
            # Rate limiting - prevent spam
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            
            if time_since_last < 0.5:  # 500ms minimum between requests
                time.sleep(0.5 - time_since_last)
                
            print(f"🤖 Asking AI: '{text[:50]}{'...' if len(text) > 50 else ''}'")
            start_time = time.time()
            
            # Send request to AgenticSeek
            response = self.session.post(
                f"{self.config.agenticseek_url}/query",
                json={"query": text},
                timeout=self.config.agenticseek_timeout
            )
            
            self.last_request_time = time.time()
            duration = self.last_request_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get("response", "").strip()
                
                print(f"✅ AI responded in {duration:.1f}s")
                print(f"   Response: '{ai_response[:100]}{'...' if len(ai_response) > 100 else ''}'")
                
                return ai_response
                
            elif response.status_code == 429:
                print("⚠️ Rate limited - waiting...")
                time.sleep(1)
                return "I'm processing too many requests. Please wait a moment."
                
            else:
                print(f"❌ AI request failed: {response.status_code}")
                return "Sorry, I'm having trouble processing that right now."
                
        except requests.exceptions.Timeout:
            print("⏰ AI request timed out")
            return "Sorry, that's taking too long to process."
            
        except requests.exceptions.ConnectionError:
            print("❌ Lost connection to AI")
            return "I've lost connection to my AI brain. Please check if AgenticSeek is running."
            
        except Exception as e:
            print(f"❌ AI request error: {e}")
            return "Sorry, I encountered an error processing that."
            
    def test_ai_response(self) -> bool:
        """Test AI response functionality"""
        print("🧪 Testing AI response...")
        
        test_query = "Hello, can you hear me?"
        response = self.get_response(test_query)
        
        if response and len(response) > 0:
            print("✅ AI response test passed")
            return True
        else:
            print("❌ AI response test failed")
            return False
            
    def cleanup(self):
        """Cleanup resources"""
        try:
            self.session.close()
            print("🧹 AI Client cleanup completed")
        except Exception as e:
            print(f"⚠️ AI Client cleanup error: {e}")
            
    def __del__(self):
        """Destructor"""
        try:
            self.cleanup()
        except:
            pass
