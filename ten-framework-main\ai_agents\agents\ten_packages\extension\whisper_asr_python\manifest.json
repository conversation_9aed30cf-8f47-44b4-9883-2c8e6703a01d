{"type": "extension", "name": "whisper_asr_python", "version": "0.1.0", "language": "python", "dependencies": [{"type": "system", "name": "ten_runtime_python", "version": "0.6.4"}], "api": {"data_in": [{"name": "pcm_frame", "property": {"sample_rate": {"type": "int64"}, "bytes_per_sample": {"type": "int64"}, "number_of_channels": {"type": "int64"}, "data_fmt": {"type": "string"}, "samples_per_channel": {"type": "int64"}}}], "data_out": [{"name": "text_data", "property": {"text": {"type": "string"}, "is_final": {"type": "bool"}, "stream_id": {"type": "int64"}, "end_of_segment": {"type": "bool"}}}]}}