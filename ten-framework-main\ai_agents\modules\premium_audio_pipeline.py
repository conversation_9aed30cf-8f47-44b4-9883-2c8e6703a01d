#!/usr/bin/env python3
"""
PREMIUM AUDIO PIPELINE - HIGH QUALITY AUDIO PROCESSING
Fixes all audio quality issues with proper sample rates and processing
"""

import numpy as np
import librosa
import soundfile as sf
import torch
import torchaudio
from scipy import signal
import warnings
warnings.filterwarnings("ignore")

class PremiumAudioPipeline:
    """Premium audio processing pipeline"""
    
    def __init__(self):
        # Premium audio settings
        self.target_sample_rate = 22050  # High quality
        self.target_channels = 1  # Mono for voice
        self.target_dtype = np.float32
        self.buffer_size = 2048  # Large buffer
        
        # Audio enhancement settings
        self.noise_reduction = True
        self.dynamic_range_compression = True
        self.audio_normalization = True
        
        print("🎵 Premium Audio Pipeline initialized")
        print(f"   Sample Rate: {self.target_sample_rate}Hz")
        print(f"   Channels: {self.target_channels}")
        print(f"   Buffer Size: {self.buffer_size}")
        
    def process_input_audio(self, audio_data: np.ndarray, original_sr: int = 16000) -> np.ndarray:
        """Process input audio for optimal quality"""
        try:
            # Ensure float32
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
                
            # Normalize input
            if np.max(np.abs(audio_data)) > 0:
                audio_data = audio_data / np.max(np.abs(audio_data))
                
            # Resample to target rate if needed
            if original_sr != self.target_sample_rate:
                audio_data = librosa.resample(
                    audio_data, 
                    orig_sr=original_sr, 
                    target_sr=self.target_sample_rate,
                    res_type='kaiser_best'  # Highest quality resampling
                )
                
            # Convert to mono if needed
            if len(audio_data.shape) > 1:
                audio_data = librosa.to_mono(audio_data)
                
            # Apply noise reduction
            if self.noise_reduction:
                audio_data = self._reduce_noise(audio_data)
                
            # Apply dynamic range compression
            if self.dynamic_range_compression:
                audio_data = self._compress_dynamic_range(audio_data)
                
            # Final normalization
            if self.audio_normalization:
                audio_data = self._normalize_audio(audio_data)
                
            return audio_data
            
        except Exception as e:
            print(f"❌ Audio processing error: {e}")
            return audio_data
            
    def process_output_audio(self, audio_data: np.ndarray, original_sr: int = 22050) -> np.ndarray:
        """Process output audio for optimal playback quality"""
        try:
            # Ensure float32
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
                
            # Resample to target rate if needed
            if original_sr != self.target_sample_rate:
                audio_data = librosa.resample(
                    audio_data, 
                    orig_sr=original_sr, 
                    target_sr=self.target_sample_rate,
                    res_type='kaiser_best'
                )
                
            # Convert to mono if needed
            if len(audio_data.shape) > 1:
                audio_data = librosa.to_mono(audio_data)
                
            # Apply audio enhancement
            audio_data = self._enhance_speech(audio_data)
            
            # Apply gentle compression for consistent volume
            audio_data = self._compress_dynamic_range(audio_data, ratio=2.0)
            
            # Final normalization with headroom
            audio_data = self._normalize_audio(audio_data, target_level=0.8)
            
            return audio_data
            
        except Exception as e:
            print(f"❌ Output processing error: {e}")
            return audio_data
            
    def _reduce_noise(self, audio_data: np.ndarray) -> np.ndarray:
        """Advanced noise reduction"""
        try:
            # Spectral subtraction for noise reduction
            stft = librosa.stft(audio_data, n_fft=2048, hop_length=512)
            magnitude = np.abs(stft)
            phase = np.angle(stft)
            
            # Estimate noise from first 0.5 seconds
            noise_frames = int(0.5 * self.target_sample_rate / 512)
            noise_spectrum = np.mean(magnitude[:, :noise_frames], axis=1, keepdims=True)
            
            # Spectral subtraction
            alpha = 2.0  # Over-subtraction factor
            beta = 0.01  # Spectral floor
            
            enhanced_magnitude = magnitude - alpha * noise_spectrum
            enhanced_magnitude = np.maximum(enhanced_magnitude, beta * magnitude)
            
            # Reconstruct audio
            enhanced_stft = enhanced_magnitude * np.exp(1j * phase)
            enhanced_audio = librosa.istft(enhanced_stft, hop_length=512)
            
            return enhanced_audio
            
        except Exception as e:
            print(f"⚠️ Noise reduction failed: {e}")
            return audio_data
            
    def _compress_dynamic_range(self, audio_data: np.ndarray, ratio: float = 4.0) -> np.ndarray:
        """Dynamic range compression"""
        try:
            # Simple compressor
            threshold = 0.3
            
            # Find peaks above threshold
            peaks = np.abs(audio_data) > threshold
            
            # Apply compression to peaks
            compressed = audio_data.copy()
            compressed[peaks] = np.sign(audio_data[peaks]) * (
                threshold + (np.abs(audio_data[peaks]) - threshold) / ratio
            )
            
            return compressed
            
        except Exception as e:
            print(f"⚠️ Compression failed: {e}")
            return audio_data
            
    def _normalize_audio(self, audio_data: np.ndarray, target_level: float = 0.9) -> np.ndarray:
        """Normalize audio to target level"""
        try:
            max_val = np.max(np.abs(audio_data))
            if max_val > 0:
                return audio_data * (target_level / max_val)
            return audio_data
            
        except Exception as e:
            print(f"⚠️ Normalization failed: {e}")
            return audio_data
            
    def _enhance_speech(self, audio_data: np.ndarray) -> np.ndarray:
        """Enhance speech quality"""
        try:
            # Pre-emphasis filter for speech clarity
            pre_emphasis = 0.97
            emphasized = np.append(audio_data[0], audio_data[1:] - pre_emphasis * audio_data[:-1])
            
            # High-pass filter to remove low-frequency noise
            sos = signal.butter(4, 80, btype='high', fs=self.target_sample_rate, output='sos')
            filtered = signal.sosfilt(sos, emphasized)
            
            # Gentle low-pass filter to remove high-frequency artifacts
            sos = signal.butter(4, 8000, btype='low', fs=self.target_sample_rate, output='sos')
            enhanced = signal.sosfilt(sos, filtered)
            
            return enhanced.astype(np.float32)
            
        except Exception as e:
            print(f"⚠️ Speech enhancement failed: {e}")
            return audio_data
            
    def convert_tensor_to_audio(self, tensor_data, sample_rate: int = 22050) -> np.ndarray:
        """Convert tensor to high-quality audio"""
        try:
            # Handle different tensor types
            if isinstance(tensor_data, torch.Tensor):
                audio_data = tensor_data.cpu().numpy()
            else:
                audio_data = np.array(tensor_data)
                
            # Ensure proper shape
            if len(audio_data.shape) > 1:
                audio_data = audio_data.squeeze()
                
            # Process for optimal quality
            return self.process_output_audio(audio_data, sample_rate)
            
        except Exception as e:
            print(f"❌ Tensor conversion error: {e}")
            return np.array([])
            
    def save_high_quality_audio(self, audio_data: np.ndarray, filename: str):
        """Save audio with optimal quality settings"""
        try:
            # Process for output
            processed_audio = self.process_output_audio(audio_data)
            
            # Save with high quality
            sf.write(
                filename, 
                processed_audio, 
                self.target_sample_rate,
                subtype='PCM_24'  # 24-bit for high quality
            )
            
        except Exception as e:
            print(f"❌ Audio save error: {e}")
            
    def get_optimal_settings(self) -> dict:
        """Get optimal audio settings"""
        return {
            'sample_rate': self.target_sample_rate,
            'channels': self.target_channels,
            'dtype': self.target_dtype,
            'buffer_size': self.buffer_size,
            'format': 'float32'
        }
        
    def validate_audio_quality(self, audio_data: np.ndarray) -> dict:
        """Validate audio quality metrics"""
        try:
            metrics = {
                'sample_rate': self.target_sample_rate,
                'duration': len(audio_data) / self.target_sample_rate,
                'max_amplitude': np.max(np.abs(audio_data)),
                'rms_level': np.sqrt(np.mean(audio_data**2)),
                'dynamic_range': np.max(audio_data) - np.min(audio_data),
                'clipping': np.sum(np.abs(audio_data) >= 0.99),
                'quality_score': self._calculate_quality_score(audio_data)
            }
            
            return metrics
            
        except Exception as e:
            print(f"❌ Quality validation error: {e}")
            return {}
            
    def _calculate_quality_score(self, audio_data: np.ndarray) -> float:
        """Calculate overall quality score (0-100)"""
        try:
            score = 100.0
            
            # Penalize clipping
            clipping_ratio = np.sum(np.abs(audio_data) >= 0.99) / len(audio_data)
            score -= clipping_ratio * 50
            
            # Penalize low RMS (too quiet)
            rms = np.sqrt(np.mean(audio_data**2))
            if rms < 0.1:
                score -= (0.1 - rms) * 200
                
            # Penalize high RMS (too loud)
            if rms > 0.8:
                score -= (rms - 0.8) * 100
                
            # Penalize low dynamic range
            dynamic_range = np.max(audio_data) - np.min(audio_data)
            if dynamic_range < 0.5:
                score -= (0.5 - dynamic_range) * 50
                
            return max(0.0, min(100.0, score))
            
        except Exception as e:
            print(f"⚠️ Quality score calculation failed: {e}")
            return 50.0
