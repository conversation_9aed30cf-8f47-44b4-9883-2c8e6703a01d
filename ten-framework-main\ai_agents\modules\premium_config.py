#!/usr/bin/env python3
"""
PREMIUM CONFIGURATION - ALL BEST FEATURES ENABLED
Complete configuration for premium voice AI system
"""

import torch
from dataclasses import dataclass
from typing import Optional, Dict, List

@dataclass
class PremiumVoiceAIConfig:
    """Premium Configuration for Voice AI system with ALL best features"""
    
    # DEVICE CONFIGURATION
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    cuda_optimization: bool = True
    memory_optimization: bool = True
    
    # PREMIUM TTS CONFIGURATION
    use_chattts: bool = True
    use_chatterbox: bool = True
    use_edge_tts: bool = True
    use_pyttsx3: bool = True
    tts_fallback_enabled: bool = True
    
    # ChatTTS Settings
    chattts_compile: bool = False
    chattts_source: str = "huggingface"
    chattts_use_decoder: bool = True
    
    # ChatterboxTTS Settings
    chatterbox_model: str = "default"
    chatterbox_device: Optional[str] = None
    
    # Edge-TTS Settings
    edge_voice: str = "en-US-AriaNeural"
    edge_rate: str = "medium"
    edge_pitch: str = "medium"
    edge_volume: str = "medium"
    
    # pyttsx3 Settings
    pyttsx3_rate: int = 180
    pyttsx3_volume: float = 1.0
    pyttsx3_voice_preference: str = "female"
    
    # PREMIUM AUDIO CONFIGURATION
    sample_rate: int = 22050  # High quality
    output_sample_rate: int = 22050
    chunk_size: int = 2048    # Large buffer
    channels: int = 1
    audio_format: str = "float32"
    buffer_size: int = 2048
    
    # Audio Enhancement
    noise_reduction: bool = True
    dynamic_range_compression: bool = True
    audio_normalization: bool = True
    speech_enhancement: bool = True
    
    # Audio Quality Settings
    target_level: float = 0.8
    compression_ratio: float = 4.0
    pre_emphasis: float = 0.97
    
    # PREMIUM VAD CONFIGURATION
    use_hubert_vad: bool = True
    hubert_vad_model: str = "HuBERT-11/distilzen-wav2vec-large-st9-mel"
    hubert_threshold: float = 0.5
    
    # WebRTC VAD (Fallback)
    webrtc_aggressiveness: int = 3
    webrtc_frame_duration: int = 30
    
    # VAD Performance
    vad_sensitivity: str = "maximum"  # maximum, high, medium, low
    vad_response_time_ms: int = 25
    
    # WHISPER STT CONFIGURATION
    whisper_model: str = "large-v3"
    whisper_device: Optional[str] = None
    whisper_language: str = "en"
    whisper_task: str = "transcribe"
    
    # STT Performance
    whisper_fp16: bool = True
    whisper_beam_size: int = 5
    whisper_best_of: int = 5
    
    # EMOTION & SSML CONFIGURATION
    emotion_detection: bool = True
    emotion_model: str = "j-hartmann/emotion-english-distilroberta-base"
    emotion_threshold: float = 0.6
    emotion_device: Optional[str] = None
    
    # SSML Settings
    ssml_enabled: bool = True
    voice_styles: bool = True
    prosody_control: bool = True
    emphasis_detection: bool = True
    
    # Emotion Mapping
    emotion_voice_modulation: bool = True
    contextual_emotions: bool = True
    
    # VOICE CLONING CONFIGURATION
    voice_cloning: bool = True
    default_speaker: str = "aria"
    voice_profiles_enabled: bool = True
    custom_voices_enabled: bool = True
    
    # Voice Quality
    voice_quality_priority: str = "premium"  # premium, balanced, fast
    speaker_adaptation: bool = True
    
    # AGENTICSEEK CONFIGURATION
    agenticseek_url: str = "http://localhost:7777"
    agenticseek_timeout: int = 30
    agenticseek_max_retries: int = 3
    agenticseek_rate_limit: float = 0.5  # Seconds between requests
    
    # PREMIUM INTERRUPTION CONFIGURATION
    interruption_enabled: bool = True
    interruption_threshold: float = 0.15  # Very sensitive
    interruption_response_time_ms: int = 10  # 10ms response
    silence_duration_ms: int = 300  # 300ms silence detection
    max_silence_duration_ms: int = 2000
    
    # Interruption Behavior
    interruption_fade_out: bool = True
    interruption_fade_time_ms: int = 50
    interruption_recovery: bool = True
    
    # PERFORMANCE CONFIGURATION
    quality_over_speed: bool = True
    parallel_processing: bool = True
    async_processing: bool = True
    
    # Memory Management
    max_memory_usage_gb: float = 4.0
    garbage_collection: bool = True
    model_caching: bool = True
    
    # SAFETY CONFIGURATION
    system_protection: bool = True
    error_recovery: bool = True
    comprehensive_logging: bool = True
    safe_mode: bool = True
    
    # Error Handling
    max_errors_before_fallback: int = 3
    automatic_recovery: bool = True
    fallback_to_basic: bool = True
    
    # TESTING CONFIGURATION
    testing_enabled: bool = True
    performance_monitoring: bool = True
    quality_validation: bool = True
    
    # Test Settings
    test_voice_quality: bool = True
    test_interruption_system: bool = True
    test_emotion_detection: bool = True
    
    def __post_init__(self):
        """Initialize and validate premium configuration"""
        
        # Set device-specific settings
        if self.chatterbox_device is None:
            self.chatterbox_device = self.device
            
        if self.whisper_device is None:
            self.whisper_device = self.device
            
        if self.emotion_device is None:
            self.emotion_device = self.device
            
        # Validate CUDA availability
        if not torch.cuda.is_available() and self.device == "cuda":
            print("⚠️ CUDA not available, switching to CPU")
            self.device = "cpu"
            self.chatterbox_device = "cpu"
            self.whisper_device = "cpu"
            self.emotion_device = "cpu"
            self.cuda_optimization = False
            
        # Adjust settings based on device
        if self.device == "cpu":
            self.whisper_fp16 = False  # FP16 not supported on CPU
            self.parallel_processing = False  # Reduce CPU load
            
        # Validate audio settings
        if self.sample_rate not in [16000, 22050, 44100, 48000]:
            print(f"⚠️ Unusual sample rate: {self.sample_rate}")
            
        # Print configuration summary
        self._print_config_summary()
        
    def _print_config_summary(self):
        """Print premium configuration summary"""
        print("🚀 PREMIUM VOICE AI CONFIGURATION")
        print("=" * 60)
        print(f"   Device: {self.device}")
        print(f"   Audio Quality: {self.sample_rate}Hz, {self.audio_format}")
        print(f"   TTS Engines: ChatTTS, ChatterboxTTS, Edge-TTS, pyttsx3")
        print(f"   Advanced Features: ✅ Emotion, ✅ SSML, ✅ Voice Cloning")
        print(f"   Premium Audio: ✅ Noise Reduction, ✅ Enhancement")
        print(f"   Interruption: {self.interruption_response_time_ms}ms response")
        print(f"   Safety: ✅ System Protection, ✅ Error Recovery")
        print("=" * 60)
        
    def get_tts_engines(self) -> List[str]:
        """Get list of enabled TTS engines"""
        engines = []
        if self.use_chattts:
            engines.append("chattts")
        if self.use_chatterbox:
            engines.append("chatterbox")
        if self.use_edge_tts:
            engines.append("edge")
        if self.use_pyttsx3:
            engines.append("pyttsx3")
        return engines
        
    def get_audio_settings(self) -> Dict:
        """Get audio configuration"""
        return {
            'sample_rate': self.sample_rate,
            'channels': self.channels,
            'chunk_size': self.chunk_size,
            'buffer_size': self.buffer_size,
            'format': self.audio_format,
            'noise_reduction': self.noise_reduction,
            'enhancement': self.speech_enhancement
        }
        
    def get_performance_settings(self) -> Dict:
        """Get performance configuration"""
        return {
            'device': self.device,
            'cuda_optimization': self.cuda_optimization,
            'parallel_processing': self.parallel_processing,
            'quality_over_speed': self.quality_over_speed,
            'memory_optimization': self.memory_optimization,
            'max_memory_gb': self.max_memory_usage_gb
        }
        
    def get_safety_settings(self) -> Dict:
        """Get safety configuration"""
        return {
            'system_protection': self.system_protection,
            'error_recovery': self.error_recovery,
            'safe_mode': self.safe_mode,
            'max_errors': self.max_errors_before_fallback,
            'automatic_recovery': self.automatic_recovery,
            'fallback_enabled': self.fallback_to_basic
        }
        
    def validate_system_requirements(self) -> Dict:
        """Validate system meets premium requirements"""
        requirements = {
            'cuda_available': torch.cuda.is_available(),
            'python_version_ok': True,  # Assume OK since we're running
            'memory_sufficient': True,  # Would need actual check
            'disk_space_ok': True,      # Would need actual check
            'all_dependencies': True    # Would need actual check
        }
        
        requirements['overall_ready'] = all(requirements.values())
        
        return requirements
