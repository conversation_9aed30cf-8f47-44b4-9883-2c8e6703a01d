#!/usr/bin/env python3
"""
PERFECT SPEECH-TO-TEXT SYSTEM
Fast, accurate, reliable speech recognition
"""

import numpy as np
import pyaudio
import webrtcvad
import whisper
import time
import threading
from collections import deque
import warnings
warnings.filterwarnings("ignore")

class PerfectSTT:
    """Perfect Speech-to-Text with fast VAD and accurate transcription"""
    
    def __init__(self, config):
        self.config = config
        self.is_listening = False
        self.audio_buffer = deque(maxlen=1000)  # Ring buffer
        
        print("🎤 INITIALIZING PERFECT SPEECH-TO-TEXT")
        print("=" * 50)
        
        # Initialize components
        self._init_audio()
        self._init_vad()
        self._init_whisper()
        
        print("✅ Perfect STT Ready")
        print("=" * 50)
        
    def _init_audio(self):
        """Initialize audio system"""
        print("🔊 Setting up audio capture...")
        
        self.audio = pyaudio.PyAudio()
        
        # Find best audio device
        self.device_index = None
        for i in range(self.audio.get_device_count()):
            info = self.audio.get_device_info_by_index(i)
            if info['maxInputChannels'] > 0:
                self.device_index = i
                print(f"   Using device: {info['name']}")
                break
                
        # Audio settings optimized for speech
        self.chunk_size = 1600  # 100ms at 16kHz (optimal for WebRTC VAD)
        self.audio_format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000  # WebRTC VAD requires 16kHz
        
        print(f"   Sample Rate: {self.rate}Hz")
        print(f"   Chunk Size: {self.chunk_size}")
        
    def _init_vad(self):
        """Initialize Voice Activity Detection"""
        print("🎯 Setting up Voice Activity Detection...")
        
        try:
            self.vad = webrtcvad.Vad(self.config.vad_aggressiveness)
            print(f"   VAD Aggressiveness: {self.config.vad_aggressiveness}")
            print("✅ WebRTC VAD ready")
        except Exception as e:
            print(f"❌ VAD initialization failed: {e}")
            self.vad = None
            
    def _init_whisper(self):
        """Initialize Whisper STT"""
        print("🧠 Loading Whisper model...")
        
        try:
            self.whisper_model = whisper.load_model(
                self.config.whisper_model,
                device=self.config.whisper_device
            )
            print(f"✅ Whisper {self.config.whisper_model} loaded on {self.config.whisper_device}")
        except Exception as e:
            print(f"❌ Whisper loading failed: {e}")
            self.whisper_model = None
            
    def listen_for_speech(self) -> str:
        """Listen for speech and return transcription"""
        
        if not self.whisper_model:
            return "Speech recognition not available"
            
        print("🎧 Listening for speech...")
        
        try:
            # Start audio stream
            stream = self.audio.open(
                format=self.audio_format,
                channels=self.channels,
                rate=self.rate,
                input=True,
                input_device_index=self.device_index,
                frames_per_buffer=self.chunk_size
            )
            
            # Speech detection variables
            speech_frames = []
            silence_count = 0
            speech_started = False
            max_silence_chunks = int(self.config.silence_duration_ms / 100)  # 100ms per chunk
            max_speech_chunks = int(self.config.max_speech_duration_s * 10)  # 10 chunks per second
            
            print("🎤 Ready - speak now...")
            
            while True:
                # Read audio chunk
                audio_chunk = stream.read(self.chunk_size, exception_on_overflow=False)
                
                # Check for speech using VAD
                is_speech = self._is_speech(audio_chunk)
                
                if is_speech:
                    if not speech_started:
                        print("🗣️ Speech detected!")
                        speech_started = True
                        
                    speech_frames.append(audio_chunk)
                    silence_count = 0
                    
                    # Prevent overly long recordings
                    if len(speech_frames) > max_speech_chunks:
                        print("⏰ Max speech duration reached")
                        break
                        
                else:
                    if speech_started:
                        silence_count += 1
                        speech_frames.append(audio_chunk)  # Include some silence
                        
                        # End of speech detected
                        if silence_count >= max_silence_chunks:
                            print("🔇 Speech ended")
                            break
                            
            stream.stop_stream()
            stream.close()
            
            if not speech_frames:
                print("❌ No speech detected")
                return ""
                
            # Convert to audio array
            audio_data = b''.join(speech_frames)
            audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            
            print(f"🎤 Captured {len(audio_array)/self.rate:.1f}s of speech")
            
            # Transcribe with Whisper
            return self._transcribe_audio(audio_array)
            
        except Exception as e:
            print(f"❌ Speech listening error: {e}")
            return ""
            
    def _is_speech(self, audio_chunk: bytes) -> bool:
        """Check if audio chunk contains speech"""
        
        if not self.vad:
            # Fallback: simple energy-based detection
            audio_array = np.frombuffer(audio_chunk, dtype=np.int16)
            energy = np.sqrt(np.mean(audio_array**2))
            return energy > 500  # Threshold for speech
            
        try:
            # WebRTC VAD requires exactly 10, 20, or 30ms of audio at 16kHz
            # Our chunk_size=1600 = 100ms, so we need to split it
            chunk_10ms = len(audio_chunk) // 10
            
            for i in range(0, len(audio_chunk), chunk_10ms):
                chunk = audio_chunk[i:i+chunk_10ms]
                if len(chunk) == chunk_10ms:
                    if self.vad.is_speech(chunk, self.rate):
                        return True
                        
            return False
            
        except Exception:
            # Fallback to energy detection
            audio_array = np.frombuffer(audio_chunk, dtype=np.int16)
            energy = np.sqrt(np.mean(audio_array**2))
            return energy > 500
            
    def _transcribe_audio(self, audio_array: np.ndarray) -> str:
        """Transcribe audio using Whisper"""
        
        try:
            print("🧠 Transcribing with Whisper...")
            start_time = time.time()
            
            # Whisper transcription
            result = self.whisper_model.transcribe(
                audio_array,
                language="en",  # Force English for speed
                task="transcribe",
                fp16=self.config.cuda_optimization,  # Use FP16 if CUDA
                verbose=False
            )
            
            transcription = result["text"].strip()
            duration = time.time() - start_time
            
            print(f"✅ Transcribed in {duration:.1f}s: '{transcription}'")
            
            return transcription
            
        except Exception as e:
            print(f"❌ Transcription error: {e}")
            return ""
            
    def test_microphone(self) -> bool:
        """Test microphone functionality"""
        print("🧪 Testing microphone...")
        
        try:
            stream = self.audio.open(
                format=self.audio_format,
                channels=self.channels,
                rate=self.rate,
                input=True,
                input_device_index=self.device_index,
                frames_per_buffer=self.chunk_size
            )
            
            # Record 1 second
            frames = []
            for _ in range(10):  # 10 chunks = 1 second
                data = stream.read(self.chunk_size)
                frames.append(data)
                
            stream.stop_stream()
            stream.close()
            
            # Check audio level
            audio_data = b''.join(frames)
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            max_amplitude = np.max(np.abs(audio_array))
            
            print(f"   Max amplitude: {max_amplitude}")
            
            if max_amplitude > 100:
                print("✅ Microphone working")
                return True
            else:
                print("⚠️ Microphone very quiet")
                return False
                
        except Exception as e:
            print(f"❌ Microphone test failed: {e}")
            return False
            
    def cleanup(self):
        """Cleanup resources"""
        try:
            if hasattr(self, 'audio'):
                self.audio.terminate()
            print("🧹 STT cleanup completed")
        except Exception as e:
            print(f"⚠️ STT cleanup error: {e}")
            
    def __del__(self):
        """Destructor"""
        try:
            self.cleanup()
        except:
            pass
