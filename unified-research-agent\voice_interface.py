#!/usr/bin/env python3
"""
VOICE INTERFACE
Chain of Thought: Audio Capture → STT → Research Processing → TTS → Audio Output
"""

import asyncio
import logging
import numpy as np
import torch
import time
from typing import Optional, Dict, List

# Audio processing
import pyaudio
import webrtcvad
import soundfile as sf

# Hugging Face models
from transformers import pipeline, AutoProcessor, AutoModelForSpeechSeq2Seq
from TTS.api import TTS

class VoiceInterface:
    """Local voice interface using Hugging Face STT/TTS"""
    
    def __init__(self, research_orchestrator):
        self.logger = logging.getLogger(__name__)
        self.research_orchestrator = research_orchestrator
        
        # Voice processing components
        self.stt_pipeline = None
        self.tts_model = None
        self.vad = None
        self.audio_interface = None
        
        # Audio settings
        self.sample_rate = 16000
        self.chunk_size = 1024
        self.channels = 1
        self.audio_format = pyaudio.paInt16
        
        # Conversation state
        self.conversation_history = []
        self.is_listening = False
        self.is_speaking = False
        
        # Device detection
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
    async def initialize(self):
        """Initialize voice interface components"""
        self.logger.info("Initializing Voice Interface")
        
        # Initialize STT (Speech-to-Text)
        await self._initialize_stt()
        
        # Initialize TTS (Text-to-Speech)
        await self._initialize_tts()
        
        # Initialize VAD (Voice Activity Detection)
        await self._initialize_vad()
        
        # Initialize audio interface
        await self._initialize_audio()
        
        self.logger.info("Voice interface ready for conversation")
    
    async def _initialize_stt(self):
        """Initialize local STT using Hugging Face Whisper"""
        try:
            self.logger.info("Loading Whisper STT model...")
            
            # Use Whisper large-v3 for best accuracy
            model_id = "openai/whisper-large-v3"
            
            # Load model with optimizations
            self.stt_pipeline = pipeline(
                "automatic-speech-recognition",
                model=model_id,
                device=0 if self.device == "cuda" else -1,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                model_kwargs={
                    "use_flash_attention_2": True if self.device == "cuda" else False
                }
            )
            
            self.logger.info(f"STT model loaded on {self.device}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize STT: {e}")
            # Fallback to smaller model
            try:
                self.stt_pipeline = pipeline(
                    "automatic-speech-recognition",
                    model="openai/whisper-base",
                    device=0 if self.device == "cuda" else -1
                )
                self.logger.info("Fallback to Whisper base model")
            except Exception as e2:
                self.logger.error(f"STT initialization completely failed: {e2}")
                raise
    
    async def _initialize_tts(self):
        """Initialize local TTS using Coqui TTS"""
        try:
            self.logger.info("Loading TTS model...")
            
            # Use XTTS v2 for high quality multilingual TTS
            model_name = "tts_models/multilingual/multi-dataset/xtts_v2"
            
            self.tts_model = TTS(model_name)
            
            if self.device == "cuda":
                self.tts_model = self.tts_model.to("cuda")
            
            self.logger.info(f"TTS model loaded on {self.device}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize TTS: {e}")
            # Fallback to simpler model
            try:
                self.tts_model = TTS("tts_models/en/ljspeech/tacotron2-DDC")
                if self.device == "cuda":
                    self.tts_model = self.tts_model.to("cuda")
                self.logger.info("Fallback to Tacotron2 model")
            except Exception as e2:
                self.logger.error(f"TTS initialization completely failed: {e2}")
                raise
    
    async def _initialize_vad(self):
        """Initialize Voice Activity Detection"""
        try:
            self.vad = webrtcvad.Vad(2)  # Moderate aggressiveness
            self.logger.info("VAD initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize VAD: {e}")
            self.vad = None
    
    async def _initialize_audio(self):
        """Initialize audio interface"""
        try:
            self.audio_interface = pyaudio.PyAudio()
            
            # Find best input device
            input_device = self._find_best_input_device()
            self.input_device_index = input_device
            
            self.logger.info(f"Audio interface initialized - Input device: {input_device}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize audio: {e}")
            raise
    
    def _find_best_input_device(self) -> Optional[int]:
        """Find the best audio input device"""
        for i in range(self.audio_interface.get_device_count()):
            try:
                device_info = self.audio_interface.get_device_info_by_index(i)
                if device_info.get('maxInputChannels', 0) > 0:
                    self.logger.info(f"Found input device {i}: {device_info.get('name')}")
                    return i
            except Exception:
                continue
        return None
    
    async def start_conversation_loop(self):
        """Start the main conversation loop"""
        self.logger.info("Starting conversation loop")
        
        # Welcome message
        await self._speak("Hello! I'm your local research assistant. I can help you with research, analysis, and questions using your local models. What would you like to know?")
        
        while True:
            try:
                # Listen for user input
                user_speech = await self._listen_for_speech()
                
                if not user_speech:
                    continue
                
                self.logger.info(f"User said: {user_speech}")
                
                # Check for exit commands
                if any(word in user_speech.lower() for word in ['goodbye', 'exit', 'quit', 'stop talking']):
                    await self._speak("Goodbye! It was great helping you with your research.")
                    break
                
                # Process through research orchestrator
                response = await self._process_user_input(user_speech)
                
                # Speak the response
                if response:
                    await self._speak(response)
                
            except KeyboardInterrupt:
                self.logger.info("Conversation interrupted by user")
                break
            except Exception as e:
                self.logger.error(f"Error in conversation loop: {e}")
                await self._speak("I encountered an error. Let me try again.")
    
    async def _listen_for_speech(self) -> Optional[str]:
        """Listen for speech and return transcription"""
        if self.is_speaking:
            return None
        
        self.is_listening = True
        
        try:
            # Open audio stream
            stream = self.audio_interface.open(
                format=self.audio_format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.input_device_index,
                frames_per_buffer=self.chunk_size
            )
            
            # Collect audio frames
            frames = []
            silence_count = 0
            speech_detected = False
            max_silence_chunks = 30  # ~2 seconds of silence to stop
            
            self.logger.info("Listening for speech...")
            
            while self.is_listening:
                try:
                    chunk = stream.read(self.chunk_size, exception_on_overflow=False)
                    
                    # Check for speech using VAD
                    is_speech = self._detect_speech(chunk)
                    
                    if is_speech:
                        if not speech_detected:
                            self.logger.info("Speech detected!")
                            speech_detected = True
                        frames.append(chunk)
                        silence_count = 0
                    elif speech_detected:
                        frames.append(chunk)
                        silence_count += 1
                        
                        # Stop after silence period
                        if silence_count >= max_silence_chunks:
                            break
                            
                except Exception as e:
                    self.logger.error(f"Error reading audio: {e}")
                    break
            
            stream.stop_stream()
            stream.close()
            
            if not frames:
                return None
            
            # Convert audio to numpy array
            audio_data = b''.join(frames)
            audio_np = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            
            # Transcribe using local STT
            return await self._transcribe_audio(audio_np)
            
        except Exception as e:
            self.logger.error(f"Error in speech listening: {e}")
            return None
        finally:
            self.is_listening = False
    
    def _detect_speech(self, audio_chunk: bytes) -> bool:
        """Detect speech in audio chunk"""
        if self.vad:
            try:
                # WebRTC VAD expects 10, 20, or 30ms chunks
                chunk_duration_ms = (len(audio_chunk) // 2) / self.sample_rate * 1000
                if chunk_duration_ms in [10, 20, 30]:
                    return self.vad.is_speech(audio_chunk, self.sample_rate)
            except Exception:
                pass
        
        # Fallback: simple energy detection
        audio_np = np.frombuffer(audio_chunk, dtype=np.int16)
        energy = np.sqrt(np.mean(audio_np.astype(np.float32) ** 2))
        return energy > 100  # Threshold for speech detection
    
    async def _transcribe_audio(self, audio_array: np.ndarray) -> Optional[str]:
        """Transcribe audio using local STT"""
        if not self.stt_pipeline:
            return None
        
        try:
            self.logger.info("Transcribing audio...")
            start_time = time.time()
            
            # Transcribe with Whisper
            result = self.stt_pipeline(
                audio_array,
                return_timestamps=False,
                generate_kwargs={
                    "language": "en",
                    "task": "transcribe"
                }
            )
            
            duration = time.time() - start_time
            text = result["text"].strip() if isinstance(result, dict) else str(result).strip()
            
            self.logger.info(f"Transcribed in {duration:.1f}s: '{text}'")
            
            return text if text else None
            
        except Exception as e:
            self.logger.error(f"Transcription error: {e}")
            return None
    
    async def _process_user_input(self, user_input: str) -> Optional[str]:
        """Process user input through research orchestrator"""
        try:
            # Add to conversation history
            self.conversation_history.append({
                "role": "user",
                "content": user_input,
                "timestamp": time.time()
            })
            
            # Keep conversation history manageable
            if len(self.conversation_history) > 20:
                self.conversation_history = self.conversation_history[-20:]
            
            # Create context for research orchestrator
            context = {
                "conversation_history": self.conversation_history[-5:],  # Last 5 exchanges
                "system_prompt": "You are a helpful research assistant. Provide clear, accurate, and useful responses based on available information."
            }
            
            # Process through research orchestrator
            result = await self.research_orchestrator.process_research_request(user_input, context)
            
            if result.get("success"):
                response_text = result.get("response", "I couldn't generate a response.")
                
                # Add to conversation history
                self.conversation_history.append({
                    "role": "assistant",
                    "content": response_text,
                    "timestamp": time.time(),
                    "execution_time": result.get("execution_time", 0),
                    "model_used": result.get("model_used")
                })
                
                return response_text
            else:
                error_msg = "I encountered an issue processing your request. Could you try rephrasing it?"
                self.conversation_history.append({
                    "role": "assistant",
                    "content": error_msg,
                    "timestamp": time.time(),
                    "error": True
                })
                return error_msg
                
        except Exception as e:
            self.logger.error(f"Error processing user input: {e}")
            return "I'm having trouble processing that request. Please try again."
    
    async def _speak(self, text: str) -> bool:
        """Generate and play speech using local TTS"""
        if not text or not text.strip():
            return False
        
        if self.is_speaking:
            return False
        
        self.is_speaking = True
        
        try:
            self.logger.info(f"Speaking: '{text[:50]}{'...' if len(text) > 50 else ''}'")
            start_time = time.time()
            
            # Generate speech with local TTS
            # Save to temporary file and play (simple implementation)
            temp_file = "temp_speech.wav"
            
            self.tts_model.tts_to_file(
                text=text,
                file_path=temp_file,
                speaker_wav=None,  # Use default voice
                language="en"
            )
            
            # Play the audio file (simple implementation)
            await self._play_audio_file(temp_file)
            
            duration = time.time() - start_time
            self.logger.info(f"Speech completed in {duration:.1f}s")
            
            return True
            
        except Exception as e:
            self.logger.error(f"TTS error: {e}")
            return False
        finally:
            self.is_speaking = False
    
    async def _play_audio_file(self, filepath: str):
        """Play audio file (simple implementation)"""
        try:
            # Load audio file
            audio_data, sample_rate = sf.read(filepath)
            
            # Convert to int16 for playback
            if audio_data.dtype != np.int16:
                audio_data = (audio_data * 32767).astype(np.int16)
            
            # Open output stream
            stream = self.audio_interface.open(
                format=pyaudio.paInt16,
                channels=1 if len(audio_data.shape) == 1 else audio_data.shape[1],
                rate=sample_rate,
                output=True
            )
            
            # Play audio in chunks
            chunk_size = 1024
            for i in range(0, len(audio_data), chunk_size):
                chunk = audio_data[i:i+chunk_size]
                stream.write(chunk.tobytes())
            
            stream.stop_stream()
            stream.close()
            
        except Exception as e:
            self.logger.error(f"Audio playback error: {e}")
    
    async def get_conversation_summary(self) -> Dict:
        """Get summary of current conversation"""
        if not self.conversation_history:
            return {"message": "No conversation history"}
        
        user_messages = [msg for msg in self.conversation_history if msg["role"] == "user"]
        assistant_messages = [msg for msg in self.conversation_history if msg["role"] == "assistant"]
        
        total_execution_time = sum(
            msg.get("execution_time", 0) for msg in assistant_messages
        )
        
        models_used = list(set(
            msg.get("model_used") for msg in assistant_messages 
            if msg.get("model_used")
        ))
        
        return {
            "total_exchanges": len(user_messages),
            "total_execution_time": f"{total_execution_time:.1f}s",
            "models_used": models_used,
            "conversation_duration": f"{(time.time() - self.conversation_history[0]['timestamp']):.0f}s" if self.conversation_history else "0s",
            "recent_topics": [msg["content"][:50] + "..." for msg in user_messages[-3:]]
        }
    
    async def cleanup(self):
        """Cleanup voice interface resources"""
        try:
            if self.audio_interface:
                self.audio_interface.terminate()
            
            # Cleanup model resources
            if hasattr(self.tts_model, 'to'):
                self.tts_model.to('cpu')
            
            self.logger.info("Voice interface cleanup complete")
            
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}") 