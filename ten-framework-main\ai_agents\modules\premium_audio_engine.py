#!/usr/bin/env python3
"""
PREMIUM AUDIO ENGINE
High-quality audio processing with noise reduction and enhancement
"""

import numpy as np
import scipy.signal
import scipy.ndimage
from typing import Tuple, Optional
import warnings
warnings.filterwarnings("ignore")

class PremiumAudioEngine:
    """Premium audio processing for crystal-clear voice quality"""
    
    def __init__(self, sample_rate: int = 22050):
        self.sample_rate = sample_rate
        self.noise_profile = None
        
        print("🎵 PREMIUM AUDIO ENGINE INITIALIZED")
        print(f"   Sample Rate: {sample_rate}Hz")
        print("   Features: Noise Reduction, Audio Enhancement, Dynamic Range")
        
    def enhance_input_audio(self, audio_data: np.ndarray) -> np.ndarray:
        """Enhance input audio for better STT recognition"""
        
        # 1. Normalize audio levels
        audio_data = self._normalize_audio(audio_data)
        
        # 2. Apply noise reduction
        audio_data = self._reduce_noise(audio_data)
        
        # 3. Apply high-pass filter to remove low-frequency noise
        audio_data = self._high_pass_filter(audio_data, cutoff=80)
        
        # 4. Apply dynamic range compression
        audio_data = self._compress_dynamic_range(audio_data)
        
        # 5. Enhance speech frequencies
        audio_data = self._enhance_speech_frequencies(audio_data)
        
        return audio_data
        
    def enhance_output_audio(self, audio_data: np.ndarray) -> np.ndarray:
        """Enhance output audio for better TTS quality"""
        
        # 1. Normalize audio levels
        audio_data = self._normalize_audio(audio_data)
        
        # 2. Apply gentle low-pass filter to smooth harsh frequencies
        audio_data = self._low_pass_filter(audio_data, cutoff=8000)
        
        # 3. Apply subtle reverb for natural sound
        audio_data = self._add_subtle_reverb(audio_data)
        
        # 4. Final normalization with headroom
        audio_data = self._normalize_with_headroom(audio_data, headroom=0.1)
        
        return audio_data
        
    def _normalize_audio(self, audio_data: np.ndarray) -> np.ndarray:
        """Normalize audio to prevent clipping"""
        max_val = np.max(np.abs(audio_data))
        if max_val > 0:
            return audio_data / max_val
        return audio_data
        
    def _reduce_noise(self, audio_data: np.ndarray) -> np.ndarray:
        """Simple spectral subtraction noise reduction"""
        
        # If we don't have a noise profile, estimate it from the first 0.5 seconds
        if self.noise_profile is None:
            noise_samples = int(0.5 * self.sample_rate)
            if len(audio_data) > noise_samples:
                self.noise_profile = np.mean(np.abs(audio_data[:noise_samples]))
            else:
                self.noise_profile = np.mean(np.abs(audio_data)) * 0.1
                
        # Apply simple noise gate
        threshold = self.noise_profile * 2
        mask = np.abs(audio_data) > threshold
        
        # Smooth the mask to avoid artifacts
        mask = scipy.ndimage.binary_dilation(mask, iterations=3)
        
        return audio_data * mask
        
    def _high_pass_filter(self, audio_data: np.ndarray, cutoff: float) -> np.ndarray:
        """Apply high-pass filter to remove low-frequency noise"""
        nyquist = self.sample_rate / 2
        normalized_cutoff = cutoff / nyquist
        
        # Design Butterworth high-pass filter
        b, a = scipy.signal.butter(4, normalized_cutoff, btype='high')
        
        # Apply filter
        return scipy.signal.filtfilt(b, a, audio_data)
        
    def _low_pass_filter(self, audio_data: np.ndarray, cutoff: float) -> np.ndarray:
        """Apply low-pass filter to smooth harsh frequencies"""
        nyquist = self.sample_rate / 2
        normalized_cutoff = cutoff / nyquist
        
        # Design Butterworth low-pass filter
        b, a = scipy.signal.butter(4, normalized_cutoff, btype='low')
        
        # Apply filter
        return scipy.signal.filtfilt(b, a, audio_data)
        
    def _compress_dynamic_range(self, audio_data: np.ndarray, 
                               threshold: float = 0.3, ratio: float = 4.0) -> np.ndarray:
        """Apply dynamic range compression"""
        
        # Simple compressor
        compressed = np.copy(audio_data)
        
        # Find samples above threshold
        above_threshold = np.abs(compressed) > threshold
        
        # Apply compression to samples above threshold
        compressed[above_threshold] = (
            np.sign(compressed[above_threshold]) * 
            (threshold + (np.abs(compressed[above_threshold]) - threshold) / ratio)
        )
        
        return compressed
        
    def _enhance_speech_frequencies(self, audio_data: np.ndarray) -> np.ndarray:
        """Enhance frequencies important for speech clarity (1-4 kHz)"""
        
        # Design band-pass filter for speech frequencies
        nyquist = self.sample_rate / 2
        low_cutoff = 1000 / nyquist
        high_cutoff = 4000 / nyquist
        
        b, a = scipy.signal.butter(2, [low_cutoff, high_cutoff], btype='band')
        
        # Extract speech frequencies
        speech_band = scipy.signal.filtfilt(b, a, audio_data)
        
        # Add enhanced speech back to original (subtle enhancement)
        return audio_data + speech_band * 0.2
        
    def _add_subtle_reverb(self, audio_data: np.ndarray) -> np.ndarray:
        """Add subtle reverb for more natural sound"""
        
        # Create simple reverb using delayed and attenuated copies
        delay_samples = int(0.03 * self.sample_rate)  # 30ms delay
        
        if len(audio_data) > delay_samples:
            reverb = np.zeros_like(audio_data)
            reverb[delay_samples:] = audio_data[:-delay_samples] * 0.15
            
            # Add multiple delays for richer reverb
            delay2_samples = int(0.07 * self.sample_rate)  # 70ms delay
            if len(audio_data) > delay2_samples:
                reverb[delay2_samples:] += audio_data[:-delay2_samples] * 0.08
                
            return audio_data + reverb
            
        return audio_data
        
    def _normalize_with_headroom(self, audio_data: np.ndarray, headroom: float = 0.1) -> np.ndarray:
        """Normalize audio with headroom to prevent clipping"""
        max_val = np.max(np.abs(audio_data))
        if max_val > 0:
            target_level = 1.0 - headroom
            return audio_data * (target_level / max_val)
        return audio_data
        
    def convert_sample_rate(self, audio_data: np.ndarray, 
                           from_rate: int, to_rate: int) -> np.ndarray:
        """High-quality sample rate conversion"""
        
        if from_rate == to_rate:
            return audio_data
            
        # Calculate resampling ratio
        ratio = to_rate / from_rate
        
        # Use scipy's high-quality resampling
        resampled = scipy.signal.resample(audio_data, int(len(audio_data) * ratio))
        
        return resampled.astype(np.float32)
        
    def ensure_stereo(self, audio_data: np.ndarray) -> np.ndarray:
        """Convert mono audio to stereo"""
        
        if len(audio_data.shape) == 1:
            # Mono to stereo
            return np.column_stack((audio_data, audio_data))
        else:
            # Already stereo
            return audio_data
            
    def ensure_format(self, audio_data: np.ndarray, 
                     target_format: str = "int16") -> np.ndarray:
        """Ensure audio is in the correct format"""
        
        if target_format == "int16":
            # Convert to 16-bit integer
            audio_data = np.clip(audio_data, -1.0, 1.0)
            return (audio_data * 32767).astype(np.int16)
        elif target_format == "float32":
            # Ensure float32
            return audio_data.astype(np.float32)
        else:
            return audio_data
            
    def get_audio_stats(self, audio_data: np.ndarray) -> dict:
        """Get audio statistics for debugging"""
        
        return {
            "length_seconds": len(audio_data) / self.sample_rate,
            "max_amplitude": np.max(np.abs(audio_data)),
            "rms_level": np.sqrt(np.mean(audio_data**2)),
            "dynamic_range": np.max(audio_data) - np.min(audio_data),
            "sample_rate": self.sample_rate,
            "shape": audio_data.shape
        }
