{"_ten": {"predefined_graphs": [{"name": "agenticseek_local_voice_assistant", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "default", "property": {"app_id": "${env:AGORA_APP_ID}", "app_certificate": "${env:AGORA_APP_CERTIFICATE|}", "channel": "agenticseek_local_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "enable_agora_asr": false, "agora_asr_session_control_file_path": "session_control.conf"}}, {"type": "extension", "name": "whisper_stt", "addon": "whisper_asr_python", "extension_group": "stt", "property": {"model": "large-v3", "device": "cuda", "language": "en", "chunk_duration": 5.0, "vad_threshold": 0.5, "enable_vad": true, "sample_rate": 16000, "channels": 1}}, {"type": "extension", "name": "ollama_llm", "addon": "openai_chatgpt_python", "extension_group": "chatgpt", "property": {"api_key": "ollama", "base_url": "http://localhost:11434/v1", "model": "deepseek-r1:14b", "vendor": "ollama", "frequency_penalty": 0.9, "greeting": "AgenticSeek Local Agent connected. I can help you with research, web browsing, and complex queries using local AI models. How can I assist you today?", "max_memory_length": 10, "max_tokens": 512, "prompt": "You are <PERSON><PERSON><PERSON><PERSON>, a privacy-focused local AI research assistant. You have access to web browsing, research capabilities, and multi-agent routing. Always prioritize accuracy and provide detailed, helpful responses. When users ask for research or web information, use your tools to provide current and accurate information.", "proxy_url": ""}}, {"type": "extension", "name": "bark_tts", "addon": "bark_tts_python", "extension_group": "tts", "property": {"voice": "v2/en_speaker_6", "device": "cuda", "sample_rate": 24000, "channels": 1, "chunk_size": 1024, "enable_offload": true, "text_temp": 0.7, "waveform_temp": 0.7}}, {"type": "extension", "name": "agenticseek_bridge", "addon": "agenticseek_bridge_python", "extension_group": "research", "property": {"agenticseek_url": "http://localhost:8080", "agenticseek_api_key": "local", "enable_web_search": true, "enable_browser_automation": true, "timeout_seconds": 30, "max_retries": 3, "work_dir": "/tmp/agenticseek_bridge"}}, {"type": "extension", "name": "interrupt_detector", "addon": "interrupt_detector_python", "extension_group": "default", "property": {}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}, {"type": "extension", "name": "vad", "addon": "ten_vad_python", "extension_group": "vad", "property": {"vad_threshold": 0.5, "sample_rate": 16000}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "ollama_llm"}]}, {"name": "on_user_left", "dest": [{"extension": "ollama_llm"}]}, {"name": "on_connection_failure", "dest": [{"extension": "ollama_llm"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "whisper_stt"}, {"extension": "vad"}]}]}, {"extension": "whisper_stt", "data": [{"name": "text_data", "dest": [{"extension": "interrupt_detector"}, {"extension": "message_collector"}]}]}, {"extension": "ollama_llm", "cmd": [{"name": "flush", "dest": [{"extension": "bark_tts"}]}], "data": [{"name": "text_data", "dest": [{"extension": "agenticseek_bridge"}, {"extension": "message_collector"}]}, {"name": "content_data", "dest": [{"extension": "message_collector"}]}]}, {"extension": "agenticseek_bridge", "data": [{"name": "text_data", "dest": [{"extension": "bark_tts"}, {"extension": "message_collector"}]}]}, {"extension": "bark_tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "interrupt_detector", "cmd": [{"name": "flush", "dest": [{"extension": "ollama_llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "ollama_llm"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "vad", "data": [{"name": "vad_data", "dest": [{"extension": "whisper_stt"}]}]}]}], "log_level": 3}}