#!/usr/bin/env python3
"""
ULTRA-PREMIUM TEXT-TO-SPEECH SYSTEM
Crystal-clear, natural, amazing voice output with performance optimization
"""

import numpy as np
import pygame
import io
import time
import threading
from concurrent.futures import ThreadPoolExecutor, TimeoutError
from typing import Optional, Dict, Any
import warnings
warnings.filterwarnings("ignore")

# Import premium audio engine
try:
    from .premium_audio_engine import PremiumAudioEngine
    PREMIUM_AUDIO_AVAILABLE = True
except ImportError:
    PREMIUM_AUDIO_AVAILABLE = False
    print("⚠️ Premium audio engine not available")

# TTS Imports with availability checking
TTS_ENGINES = {}

try:
    import ChatTTS
    TTS_ENGINES['chattts'] = True
    print("✅ ChatTTS available - Premium conversational quality")
except ImportError:
    TTS_ENGINES['chattts'] = False
    print("⚠️ ChatTTS not available")

try:
    import edge_tts
    import asyncio
    TTS_ENGINES['edge'] = True
    print("✅ Edge-TTS available - Microsoft Neural voices")
except ImportError:
    TTS_ENGINES['edge'] = False
    print("⚠️ Edge-TTS not available")

try:
    from chatterbox.tts import ChatterboxTTS
    TTS_ENGINES['chatterbox'] = True
    print("✅ ChatterboxTTS available - ElevenLabs alternative")
except ImportError:
    TTS_ENGINES['chatterbox'] = False
    print("⚠️ ChatterboxTTS not available")

# Always available fallback
TTS_ENGINES['windows'] = True
print("✅ Windows TTS available - System fallback")

class UltraPremiumTTS:
    """Ultra-premium Text-to-Speech with crystal-clear, natural voice"""

    def __init__(self, config):
        self.config = config
        self.is_speaking = False
        self.speech_queue = []
        self.current_speech_thread = None
        self.speech_cache = {}  # Cache for frequently used phrases

        print("🎵 INITIALIZING ULTRA-PREMIUM TTS SYSTEM")
        print("=" * 60)

        # Initialize premium audio engine
        self._init_premium_audio()

        # Initialize high-quality audio playback
        self._init_premium_audio_playback()

        # Initialize TTS engines with performance optimization
        self._init_optimized_tts_engines()

        # Initialize background processing
        self._init_background_processing()

        print("✅ Ultra-Premium TTS Ready - Crystal Clear Voice")
        print("=" * 60)

    def _init_premium_audio(self):
        """Initialize premium audio processing"""
        if PREMIUM_AUDIO_AVAILABLE:
            self.audio_engine = PremiumAudioEngine(self.config.sample_rate)
            print("✅ Premium audio engine loaded")
        else:
            self.audio_engine = None
            print("⚠️ Using basic audio processing")

    def _init_premium_audio_playback(self):
        """Initialize premium audio playback system"""
        print("🔊 Setting up premium audio playback...")

        try:
            # Initialize pygame mixer with premium settings
            pygame.mixer.pre_init(
                frequency=self.config.sample_rate,
                size=-16,  # 16-bit signed
                channels=2,  # Stereo
                buffer=1024  # Smaller buffer for lower latency
            )
            pygame.mixer.init()

            # Set high-quality mixing
            pygame.mixer.set_num_channels(8)  # More channels for better mixing

            print(f"   Sample Rate: {self.config.sample_rate}Hz")
            print(f"   Format: 16-bit stereo")
            print(f"   Buffer: 1024 samples (low latency)")
            print(f"   Channels: 8 (premium mixing)")
            print("✅ Premium audio playback ready")

        except Exception as e:
            print(f"❌ Premium audio initialization failed: {e}")
            # Fallback to basic settings
            try:
                pygame.mixer.init()
                print("✅ Basic audio playback ready (fallback)")
            except Exception as e2:
                print(f"❌ Audio initialization completely failed: {e2}")

    def _init_background_processing(self):
        """Initialize background processing for performance"""
        self.executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="TTS")
        print("✅ Background TTS processing ready")
            
    def _init_tts_engines(self):
        """Initialize TTS engines"""
        self.tts_engines = {}
        
        # Initialize ChatTTS (Primary - Best Quality)
        if CHATTTS_AVAILABLE and self.config.primary_tts == "chattts":
            print("🎭 Loading ChatTTS (Premium Quality)...")
            try:
                self.chattts = ChatTTS.Chat()
                self.chattts.load(compile=False, source="huggingface")
                
                # Configure for best quality
                self.chattts_params = ChatTTS.Chat.InferParams(
                    spk_emb=None,  # Use default speaker
                    temperature=0.3,  # Stable voice
                    top_P=0.7,
                    top_K=20,
                    audio_seed=42,  # Consistent voice
                    text_seed=42,
                    do_text_normalization=True,
                    lang="en"
                )
                
                self.tts_engines["chattts"] = True
                print("✅ ChatTTS ready - Premium conversation quality")
                
            except Exception as e:
                print(f"❌ ChatTTS failed: {e}")
                self.tts_engines["chattts"] = False
                
        # Initialize ChatterboxTTS (High Quality Alternative)
        if CHATTERBOX_AVAILABLE:
            print("🎪 Loading ChatterboxTTS (ElevenLabs Alternative)...")
            try:
                self.chatterbox = ChatterboxTTS.from_pretrained(device=self.config.device)
                self.tts_engines["chatterbox"] = True
                print("✅ ChatterboxTTS ready - ElevenLabs quality")
                
            except Exception as e:
                print(f"❌ ChatterboxTTS failed: {e}")
                self.tts_engines["chatterbox"] = False
                
        # Initialize Edge-TTS (Fast, Natural)
        if EDGE_TTS_AVAILABLE:
            print("🌐 Setting up Edge-TTS (Microsoft Neural)...")
            try:
                # Best English voices
                self.edge_voices = [
                    "en-US-AriaNeural",      # Natural female
                    "en-US-JennyNeural",     # Friendly female
                    "en-US-GuyNeural",       # Natural male
                    "en-US-DavisNeural"      # Professional male
                ]
                self.current_edge_voice = self.edge_voices[0]  # Default to Aria
                self.tts_engines["edge"] = True
                print(f"✅ Edge-TTS ready - Voice: {self.current_edge_voice}")
                
            except Exception as e:
                print(f"❌ Edge-TTS failed: {e}")
                self.tts_engines["edge"] = False
                
        # Report available engines
        available = [name for name, status in self.tts_engines.items() if status]
        print(f"🎵 Available TTS: {', '.join(available) if available else 'None'}")
        
    def speak(self, text: str) -> bool:
        """Speak text with amazing voice quality"""
        
        if not text or not text.strip():
            return False
            
        if self.is_speaking:
            print("⚠️ Already speaking, skipping...")
            return False
            
        self.is_speaking = True
        
        try:
            # Clean text for better speech
            clean_text = self._clean_text_for_speech(text)
            print(f"🎵 Speaking: '{clean_text[:100]}{'...' if len(clean_text) > 100 else ''}'")
            
            # Try primary TTS first
            if self.config.primary_tts == "chattts" and self.tts_engines.get("chattts"):
                success = self._speak_chattts(clean_text)
                if success:
                    return True
                    
            # Try ChatterboxTTS
            if self.tts_engines.get("chatterbox"):
                success = self._speak_chatterbox(clean_text)
                if success:
                    return True
                    
            # Try Edge-TTS fallback
            if self.tts_engines.get("edge"):
                success = self._speak_edge_tts(clean_text)
                if success:
                    return True
                    
            # Final fallback - Windows TTS
            return self._speak_windows_fallback(clean_text)
            
        except Exception as e:
            print(f"❌ Speech error: {e}")
            return False
            
        finally:
            self.is_speaking = False
            
    def _clean_text_for_speech(self, text: str) -> str:
        """Clean text for better speech synthesis"""
        
        # Remove problematic characters
        text = text.replace('"', "'")
        text = text.replace('`', "'")
        text = text.replace('\n', ' ')
        text = text.replace('\r', ' ')
        text = text.replace('\t', ' ')
        
        # Fix common speech issues
        text = text.replace('&', 'and')
        text = text.replace('@', 'at')
        text = text.replace('#', 'number')
        text = text.replace('%', 'percent')
        
        # Remove extra spaces
        while '  ' in text:
            text = text.replace('  ', ' ')
            
        return text.strip()
        
    def _speak_chattts(self, text: str) -> bool:
        """Speak using ChatTTS"""
        try:
            print("🎭 Using ChatTTS...")
            start_time = time.time()
            
            # Generate speech
            audio_arrays = self.chattts.infer([text], params_infer_code=self.chattts_params)
            
            if audio_arrays and len(audio_arrays) > 0:
                audio_data = audio_arrays[0]
                
                # Convert to proper format for pygame
                audio_data = np.array(audio_data, dtype=np.float32)
                
                # Normalize and convert to int16
                audio_data = audio_data / np.max(np.abs(audio_data))
                audio_int16 = (audio_data * 32767).astype(np.int16)
                
                # Convert mono to stereo
                if len(audio_int16.shape) == 1:
                    audio_stereo = np.column_stack((audio_int16, audio_int16))
                else:
                    audio_stereo = audio_int16
                    
                # Play audio
                sound = pygame.sndarray.make_sound(audio_stereo)
                sound.play()
                
                # Wait for completion
                while pygame.mixer.get_busy():
                    time.sleep(0.1)
                    
                duration = time.time() - start_time
                print(f"✅ ChatTTS completed in {duration:.1f}s")
                return True
                
        except Exception as e:
            print(f"❌ ChatTTS error: {e}")
            
        return False
        
    def _speak_chatterbox(self, text: str) -> bool:
        """Speak using ChatterboxTTS"""
        try:
            print("🎪 Using ChatterboxTTS...")
            start_time = time.time()
            
            # Generate speech
            audio_tensor = self.chatterbox.generate(text)
            
            if audio_tensor is not None:
                # Convert tensor to numpy
                audio_data = audio_tensor.cpu().numpy()
                
                # Normalize and convert to int16
                audio_data = audio_data / np.max(np.abs(audio_data))
                audio_int16 = (audio_data * 32767).astype(np.int16)
                
                # Convert mono to stereo
                if len(audio_int16.shape) == 1:
                    audio_stereo = np.column_stack((audio_int16, audio_int16))
                else:
                    audio_stereo = audio_int16
                    
                # Play audio
                sound = pygame.sndarray.make_sound(audio_stereo)
                sound.play()
                
                # Wait for completion
                while pygame.mixer.get_busy():
                    time.sleep(0.1)
                    
                duration = time.time() - start_time
                print(f"✅ ChatterboxTTS completed in {duration:.1f}s")
                return True
                
        except Exception as e:
            print(f"❌ ChatterboxTTS error: {e}")
            
        return False
        
    def _speak_edge_tts(self, text: str) -> bool:
        """Speak using Edge-TTS"""
        try:
            print("🌐 Using Edge-TTS...")
            start_time = time.time()
            
            # Generate speech asynchronously
            async def generate_speech():
                communicate = edge_tts.Communicate(text, self.current_edge_voice)
                audio_data = b""
                async for chunk in communicate.stream():
                    if chunk["type"] == "audio":
                        audio_data += chunk["data"]
                return audio_data
                
            # Run async function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            audio_data = loop.run_until_complete(generate_speech())
            loop.close()
            
            if audio_data:
                # Play audio using pygame
                audio_file = io.BytesIO(audio_data)
                pygame.mixer.music.load(audio_file)
                pygame.mixer.music.play()
                
                # Wait for completion
                while pygame.mixer.music.get_busy():
                    time.sleep(0.1)
                    
                duration = time.time() - start_time
                print(f"✅ Edge-TTS completed in {duration:.1f}s")
                return True
                
        except Exception as e:
            print(f"❌ Edge-TTS error: {e}")
            
        return False
        
    def _speak_windows_fallback(self, text: str) -> bool:
        """Fallback to Windows TTS"""
        try:
            print("🪟 Using Windows TTS fallback...")
            
            import subprocess
            
            # Use Windows built-in TTS
            command = f'powershell -Command "Add-Type -AssemblyName System.Speech; $synth = New-Object System.Speech.Synthesis.SpeechSynthesizer; $synth.SelectVoice(\'Microsoft Zira Desktop\'); $synth.Speak(\'{text}\')"'
            
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Windows TTS completed")
                return True
            else:
                print(f"❌ Windows TTS failed: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Windows TTS error: {e}")
            
        return False
        
    def test_voice(self) -> bool:
        """Test voice output"""
        print("🧪 Testing voice output...")
        
        test_text = "Hello! I am your AI assistant. Can you hear me clearly?"
        success = self.speak(test_text)
        
        if success:
            print("✅ Voice test passed")
        else:
            print("❌ Voice test failed")
            
        return success
        
    def cleanup(self):
        """Cleanup resources"""
        try:
            pygame.mixer.quit()
            print("🧹 TTS cleanup completed")
        except Exception as e:
            print(f"⚠️ TTS cleanup error: {e}")
            
    def __del__(self):
        """Destructor"""
        try:
            self.cleanup()
        except:
            pass
