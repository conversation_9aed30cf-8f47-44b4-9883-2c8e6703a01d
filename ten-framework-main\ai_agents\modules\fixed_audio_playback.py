#!/usr/bin/env python3
"""
FIXED AUDIO PLAYBACK SYSTEM
Reliable audio playback for premium voice AI
"""

import os
import time
import numpy as np
import pygame
import soundfile as sf
import tempfile
import subprocess
from typing import Optional
import warnings
warnings.filterwarnings("ignore")

class FixedAudioPlayback:
    """Reliable audio playback system with multiple fallbacks"""
    
    def __init__(self, sample_rate: int = 22050):
        self.sample_rate = sample_rate
        self.is_initialized = False
        self.playback_method = None
        
        # Initialize audio systems
        self._init_audio_systems()
        
    def _init_audio_systems(self):
        """Initialize audio playback systems with fallbacks"""
        
        # Try pygame first
        try:
            # Quit any existing mixer
            pygame.mixer.quit()

            # Initialize with specific settings
            pygame.mixer.pre_init(
                frequency=self.sample_rate,
                size=-16,
                channels=2,  # Use stereo for compatibility
                buffer=2048
            )
            pygame.mixer.init()

            # Verify initialization
            mixer_info = pygame.mixer.get_init()
            print(f"🎵 Pygame mixer: {mixer_info}")

            self.playback_method = "pygame"
            self.is_initialized = True
            print("✅ Pygame audio initialized")
            return
        except Exception as e:
            print(f"⚠️ Pygame init failed: {e}")
            
        # Try system audio as fallback
        try:
            # Test system audio
            self._test_system_audio()
            self.playback_method = "system"
            self.is_initialized = True
            print("✅ System audio initialized")
            return
        except Exception as e:
            print(f"⚠️ System audio failed: {e}")
            
        print("❌ No audio playback available")
        
    def _test_system_audio(self):
        """Test system audio playback"""
        # Create a test audio file
        test_audio = np.sin(2 * np.pi * 440 * np.linspace(0, 0.1, int(self.sample_rate * 0.1)))
        
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            sf.write(temp_file.name, test_audio, self.sample_rate)
            temp_path = temp_file.name
            
        try:
            # Try to play with system command
            if os.name == 'nt':  # Windows
                subprocess.run(['powershell', '-c', f'(New-Object Media.SoundPlayer "{temp_path}").PlaySync()'], 
                             check=True, capture_output=True, timeout=2)
            else:  # Linux/Mac
                subprocess.run(['aplay', temp_path], check=True, capture_output=True, timeout=2)
                
        finally:
            # Clean up test file
            try:
                os.unlink(temp_path)
            except:
                pass
                
    def play_audio_data(self, audio_data: np.ndarray, sample_rate: Optional[int] = None) -> bool:
        """Play audio data with automatic format conversion"""
        
        if not self.is_initialized:
            print("❌ Audio system not initialized")
            return False
            
        if sample_rate is None:
            sample_rate = self.sample_rate
            
        try:
            # Ensure audio is in correct format
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
                
            # Normalize audio
            if np.max(np.abs(audio_data)) > 0:
                audio_data = audio_data / np.max(np.abs(audio_data)) * 0.8
                
            # Resample if needed
            if sample_rate != self.sample_rate:
                audio_data = self._resample_audio(audio_data, sample_rate, self.sample_rate)
                
            # Play using selected method
            if self.playback_method == "pygame":
                return self._play_pygame(audio_data)
            elif self.playback_method == "system":
                return self._play_system(audio_data)
            else:
                print("❌ No playback method available")
                return False
                
        except Exception as e:
            print(f"❌ Audio playback error: {e}")
            return False
            
    def _play_pygame(self, audio_data: np.ndarray) -> bool:
        """Play audio using pygame"""
        try:
            # Convert to int16 for pygame
            audio_int16 = (audio_data * 32767).astype(np.int16)

            # Get mixer info to determine format
            mixer_info = pygame.mixer.get_init()
            if mixer_info is None:
                print("❌ Pygame mixer not initialized")
                return False

            freq, size, channels = mixer_info

            # Format audio for pygame mixer
            if channels == 2:  # Stereo
                if len(audio_int16.shape) == 1:
                    # Mono to stereo - duplicate channel
                    audio_int16 = np.column_stack((audio_int16, audio_int16))
                elif len(audio_int16.shape) == 2 and audio_int16.shape[1] == 1:
                    # Mono column to stereo
                    audio_int16 = np.column_stack((audio_int16.flatten(), audio_int16.flatten()))
            else:  # Mono
                if len(audio_int16.shape) == 2:
                    # Stereo to mono
                    audio_int16 = np.mean(audio_int16, axis=1).astype(np.int16)

            # Create pygame sound
            sound = pygame.sndarray.make_sound(audio_int16)

            # Play sound
            sound.play()

            # Wait for playback to complete
            while pygame.mixer.get_busy():
                time.sleep(0.01)

            print("🔊 Audio played successfully (pygame)")
            return True

        except Exception as e:
            print(f"❌ Pygame playback error: {e}")
            return False
            
    def _play_system(self, audio_data: np.ndarray) -> bool:
        """Play audio using system commands"""
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                sf.write(temp_file.name, audio_data, self.sample_rate)
                temp_path = temp_file.name
                
            try:
                # Play with system command
                if os.name == 'nt':  # Windows
                    # Use PowerShell with proper escaping
                    cmd = ['powershell', '-c', f'(New-Object Media.SoundPlayer "{temp_path}").PlaySync()']
                    result = subprocess.run(cmd, capture_output=True, timeout=30)
                    
                    if result.returncode == 0:
                        print("🔊 Audio played successfully (Windows)")
                        return True
                    else:
                        print(f"❌ Windows audio error: {result.stderr.decode()}")
                        return False
                        
                else:  # Linux/Mac
                    subprocess.run(['aplay', temp_path], check=True, timeout=30)
                    print("🔊 Audio played successfully (Linux)")
                    return True
                    
            finally:
                # Clean up temp file
                try:
                    os.unlink(temp_path)
                except:
                    pass
                    
        except subprocess.TimeoutExpired:
            print("❌ Audio playback timeout")
            return False
        except Exception as e:
            print(f"❌ System playback error: {e}")
            return False
            
    def _resample_audio(self, audio_data: np.ndarray, from_rate: int, to_rate: int) -> np.ndarray:
        """Simple audio resampling"""
        if from_rate == to_rate:
            return audio_data
            
        # Simple linear interpolation resampling
        ratio = to_rate / from_rate
        new_length = int(len(audio_data) * ratio)
        
        # Create new time indices
        old_indices = np.linspace(0, len(audio_data) - 1, len(audio_data))
        new_indices = np.linspace(0, len(audio_data) - 1, new_length)
        
        # Interpolate
        resampled = np.interp(new_indices, old_indices, audio_data)
        
        return resampled.astype(np.float32)
        
    def play_audio_file(self, file_path: str) -> bool:
        """Play audio file directly"""
        try:
            if not os.path.exists(file_path):
                print(f"❌ Audio file not found: {file_path}")
                return False
                
            # Load audio file
            audio_data, sample_rate = sf.read(file_path)
            
            # Play audio data
            return self.play_audio_data(audio_data, sample_rate)
            
        except Exception as e:
            print(f"❌ File playback error: {e}")
            return False
            
    def test_audio_playback(self) -> bool:
        """Test audio playback with a simple tone"""
        try:
            # Generate test tone (440Hz for 0.5 seconds)
            duration = 0.5
            t = np.linspace(0, duration, int(self.sample_rate * duration))
            test_audio = 0.3 * np.sin(2 * np.pi * 440 * t)
            
            print("🧪 Testing audio playback...")
            success = self.play_audio_data(test_audio)
            
            if success:
                print("✅ Audio playback test successful")
            else:
                print("❌ Audio playback test failed")
                
            return success
            
        except Exception as e:
            print(f"❌ Audio test error: {e}")
            return False
            
    def get_playback_info(self) -> dict:
        """Get audio playback system information"""
        return {
            'initialized': self.is_initialized,
            'method': self.playback_method,
            'sample_rate': self.sample_rate,
            'pygame_available': self._check_pygame(),
            'system_audio_available': self._check_system_audio()
        }
        
    def _check_pygame(self) -> bool:
        """Check if pygame is available"""
        try:
            pygame.mixer.get_init()
            return True
        except:
            return False
            
    def _check_system_audio(self) -> bool:
        """Check if system audio is available"""
        try:
            if os.name == 'nt':
                result = subprocess.run(['powershell', '-c', 'Get-Command New-Object'], 
                                      capture_output=True, timeout=5)
                return result.returncode == 0
            else:
                result = subprocess.run(['which', 'aplay'], capture_output=True, timeout=5)
                return result.returncode == 0
        except:
            return False
            
    def stop_playback(self):
        """Stop current playback"""
        try:
            if self.playback_method == "pygame":
                pygame.mixer.stop()
                print("🛑 Pygame playback stopped")
        except Exception as e:
            print(f"⚠️ Stop playback error: {e}")
            
    def cleanup(self):
        """Cleanup audio resources"""
        try:
            self.stop_playback()
            if self.playback_method == "pygame":
                pygame.mixer.quit()
                print("🧹 Audio system cleaned up")
        except Exception as e:
            print(f"⚠️ Cleanup error: {e}")
            
    def __del__(self):
        """Destructor"""
        try:
            self.cleanup()
        except:
            pass
