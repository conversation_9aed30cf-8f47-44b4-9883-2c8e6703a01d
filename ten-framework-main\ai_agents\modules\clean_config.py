#!/usr/bin/env python3
"""
CLEAN VOICE AI CONFIGURATION
Minimal, fast, essential settings only
"""

import torch
from dataclasses import dataclass
from typing import List

@dataclass
class CleanVoiceAIConfig:
    """Clean, minimal voice AI configuration"""
    
    # CORE SETTINGS
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    sample_rate: int = 22050  # High quality audio
    audio_format: str = "float32"
    
    # SPEECH-TO-TEXT SETTINGS
    whisper_model: str = "large-v3"  # Best accuracy
    whisper_device: str = "cuda" if torch.cuda.is_available() else "cpu"
    
    # VOICE ACTIVITY DETECTION
    vad_aggressiveness: int = 3  # Most aggressive
    silence_duration_ms: int = 800  # 800ms silence = end of speech
    max_speech_duration_s: int = 30  # Max 30 seconds per speech
    
    # TEXT-TO-SPEECH SETTINGS
    primary_tts: str = "chattts"  # Best quality
    fallback_tts: str = "edge"    # Fast fallback
    
    # AGENTICSEEK INTEGRATION
    agenticseek_url: str = "http://localhost:7777"
    agenticseek_timeout: int = 15  # Fast timeout
    
    # PERFORMANCE SETTINGS
    fast_mode: bool = True  # Prioritize speed
    cuda_optimization: bool = True
    
    def __post_init__(self):
        """Validate configuration"""
        if self.device == "cuda" and not torch.cuda.is_available():
            print("⚠️ CUDA not available, using CPU")
            self.device = "cpu"
            self.whisper_device = "cpu"
            self.cuda_optimization = False
            
        print("✅ Clean Voice AI Config Ready")
        print(f"   Device: {self.device}")
        print(f"   Audio: {self.sample_rate}Hz")
        print(f"   STT: Whisper {self.whisper_model}")
        print(f"   TTS: {self.primary_tts}")
        print(f"   Fast Mode: {self.fast_mode}")
