#!/usr/bin/env python3
"""
PERFECT VOICE AI CONFIGURATION
2025 Hugging Face models with ultra-fast interruption system
"""

import torch
from dataclasses import dataclass
from typing import List

@dataclass
class PerfectVoiceAIConfig:
    """Perfect voice AI configuration with 2025 models and interruption system"""

    # CORE SETTINGS
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    sample_rate: int = 22050  # High quality audio
    audio_format: str = "float32"

    # 2025 HUGGING FACE SPEECH-TO-TEXT SETTINGS
    whisper_model: str = "openai/whisper-large-v3-turbo"  # Latest 2025 model
    whisper_device: str = "cuda" if torch.cuda.is_available() else "cpu"
    use_faster_whisper: bool = True  # Ultra-fast inference

    # 2025 HUGGING FACE TEXT-TO-SPEECH SETTINGS
    primary_tts: str = "coqui"  # Coqui TTS for best quality
    tts_model: str = "tts_models/en/ljspeech/tacotron2-DDC"  # High quality model
    fallback_tts: str = "edge"    # Fast fallback

    # ULTRA-FAST INTERRUPTION SYSTEM
    interruption_enabled: bool = True
    interruption_threshold_ms: int = 50  # 50ms ultra-fast detection
    interruption_sensitivity: float = 0.3  # High sensitivity
    real_time_processing: bool = True

    # ADVANCED VOICE ACTIVITY DETECTION
    vad_aggressiveness: int = 3  # Most aggressive
    vad_frame_duration_ms: int = 10  # 10ms frames for ultra-fast detection
    silence_duration_ms: int = 400  # 400ms silence = end of speech (faster)
    max_speech_duration_s: int = 30  # Max 30 seconds per speech

    # AGENTICSEEK INTEGRATION
    agenticseek_url: str = "http://localhost:7777"
    agenticseek_timeout: int = 8  # Faster timeout

    # PERFORMANCE SETTINGS
    fast_mode: bool = True  # Prioritize speed
    cuda_optimization: bool = True
    parallel_processing: bool = True  # Enable parallel processing
    audio_enhancement: bool = True  # Enable premium audio processing
    
    def __post_init__(self):
        """Validate configuration"""
        if self.device == "cuda" and not torch.cuda.is_available():
            print("⚠️ CUDA not available, using CPU")
            self.device = "cpu"
            self.whisper_device = "cpu"
            self.cuda_optimization = False
            
        print("✅ Clean Voice AI Config Ready")
        print(f"   Device: {self.device}")
        print(f"   Audio: {self.sample_rate}Hz")
        print(f"   STT: Whisper {self.whisper_model}")
        print(f"   TTS: {self.primary_tts}")
        print(f"   Fast Mode: {self.fast_mode}")
