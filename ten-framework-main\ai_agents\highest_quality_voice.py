#!/usr/bin/env python3
"""
HIGHEST QUALITY VOICE AI
- STT: openai-whisper (most accurate)
- AI: AgenticSeek (research AI)
- TTS: chattts (best quality)
SIMPLE - NO BULLSHIT
"""

import whisper
import ChatTTS
import requests
import pyaudio
import numpy as np
import pygame
import time
import warnings
warnings.filterwarnings("ignore")

class HighestQualityVoice:
    def __init__(self):
        print("🏆 HIGHEST QUALITY VOICE AI")
        print("=" * 40)
        
        # Initialize components
        self._init_whisper()
        self._init_chattts() 
        self._init_agenticseek()
        self._init_audio()
        
        print("🚀 HIGHEST QUALITY SYSTEM READY!")
        
    def _init_whisper(self):
        """Load Whisper for highest quality STT"""
        print("🎤 Loading Whisper (highest quality)...")
        self.whisper_model = whisper.load_model("large-v3", device="cuda")
        print("✅ Whisper large-v3 loaded")
        
    def _init_chattts(self):
        """Load ChatTTS for highest quality TTS"""
        print("🎵 Loading ChatTTS (highest quality)...")
        self.chattts = ChatTTS.Chat()
        self.chattts.load(compile=False, source="huggingface")
        print("✅ ChatTTS loaded")
        
    def _init_agenticseek(self):
        """Setup AgenticSeek connection"""
        print("🤖 Connecting to AgenticSeek...")
        self.agenticseek_url = "http://localhost:7777"
        self.session = requests.Session()
        
        # Test connection
        try:
            response = self.session.get(f"{self.agenticseek_url}/health", timeout=3)
            if response.status_code == 200:
                print("✅ AgenticSeek connected")
            else:
                print("⚠️ AgenticSeek connection issues")
        except:
            print("❌ AgenticSeek not running - start it manually")
            
    def _init_audio(self):
        """Setup audio capture and playback"""
        print("🔊 Setting up audio...")
        
        # Audio capture
        self.audio = pyaudio.PyAudio()
        self.sample_rate = 16000
        self.chunk_size = 1600
        
        # Audio playback
        pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=1024)
        print("✅ Audio ready")
        
    def listen(self):
        """Capture speech with highest quality"""
        print("🎧 Listening...")
        
        # Start recording
        stream = self.audio.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=self.sample_rate,
            input=True,
            frames_per_buffer=self.chunk_size
        )
        
        print("🎤 Speak now...")
        
        # Record audio
        frames = []
        silence_count = 0
        speech_started = False
        
        while True:
            data = stream.read(self.chunk_size, exception_on_overflow=False)
            audio_array = np.frombuffer(data, dtype=np.int16)
            
            # Simple voice detection
            energy = np.sqrt(np.mean(audio_array**2))
            
            if energy > 50:  # Speech detected (lowered threshold)
                if not speech_started:
                    print("🗣️ Speech detected!")
                    speech_started = True
                frames.append(data)
                silence_count = 0
            else:
                if speech_started:
                    silence_count += 1
                    frames.append(data)
                    if silence_count > 10:  # 1 second silence
                        print("🔇 Speech ended")
                        break
                        
        stream.stop_stream()
        stream.close()
        
        if not frames:
            return ""
            
        # Convert to audio array
        audio_data = b''.join(frames)
        audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
        
        # Transcribe with Whisper (highest quality)
        print("🧠 Transcribing with Whisper...")
        result = self.whisper_model.transcribe(audio_array, language="en")
        text = result["text"].strip()
        
        print(f"👤 You said: '{text}'")
        return text
        
    def think(self, text):
        """Get AI response from AgenticSeek"""
        if not text:
            return "I didn't hear anything."
            
        print("🤖 AgenticSeek thinking...")
        
        try:
            response = self.session.post(
                f"{self.agenticseek_url}/query",
                json={"query": text},
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_text = result.get("response", "").strip()
                print(f"🧠 AI: {ai_text}")
                return ai_text
            else:
                return "I'm having trouble thinking right now."
                
        except Exception as e:
            print(f"❌ AI error: {e}")
            return "Sorry, I'm having connection issues."
            
    def speak(self, text):
        """Speak with highest quality ChatTTS"""
        if not text:
            return
            
        print(f"🎵 Speaking: '{text[:50]}...'")
        
        try:
            # Generate speech with ChatTTS
            audio_arrays = self.chattts.infer([text])
            
            if audio_arrays and len(audio_arrays) > 0:
                audio_data = audio_arrays[0]
                
                # Convert to proper format
                audio_data = np.array(audio_data, dtype=np.float32)
                
                # Normalize audio
                if np.max(np.abs(audio_data)) > 0:
                    audio_data = audio_data / np.max(np.abs(audio_data))
                
                # Convert to 16-bit stereo for pygame
                audio_16bit = (audio_data * 32767).astype(np.int16)
                
                # Make stereo
                if len(audio_16bit.shape) == 1:
                    audio_stereo = np.column_stack((audio_16bit, audio_16bit))
                else:
                    audio_stereo = audio_16bit
                    
                # Play audio
                sound = pygame.sndarray.make_sound(audio_stereo)
                sound.play()
                
                # Wait for completion
                while pygame.mixer.get_busy():
                    time.sleep(0.1)
                    
                print("✅ Speech completed")
                
        except Exception as e:
            print(f"❌ Speech error: {e}")
            
    def conversation(self):
        """Start highest quality conversation"""
        print("\n🏆 STARTING HIGHEST QUALITY CONVERSATION")
        print("=" * 50)
        print("💬 Ready for conversation!")
        print("   (Say 'exit' to quit)")
        
        conversation_count = 0
        
        try:
            while True:
                conversation_count += 1
                print(f"\n--- Conversation {conversation_count} ---")
                
                # 1. Listen (highest quality)
                user_text = self.listen()
                
                if not user_text:
                    continue
                    
                # Check exit
                if any(word in user_text.lower() for word in ['exit', 'quit', 'goodbye']):
                    print("👋 Goodbye!")
                    self.speak("Goodbye! It was great talking with you.")
                    break
                    
                # 2. Think (AgenticSeek)
                ai_response = self.think(user_text)
                
                # 3. Speak (highest quality)
                self.speak(ai_response)
                
        except KeyboardInterrupt:
            print("\n👋 Conversation ended")
            
        except Exception as e:
            print(f"\n❌ Error: {e}")
            
        finally:
            self.cleanup()
            
    def cleanup(self):
        """Cleanup resources"""
        try:
            self.audio.terminate()
            pygame.mixer.quit()
            self.session.close()
            print("🧹 Cleanup completed")
        except:
            pass

def main():
    try:
        voice_ai = HighestQualityVoice()
        voice_ai.conversation()
    except Exception as e:
        print(f"❌ System error: {e}")

if __name__ == "__main__":
    main()
