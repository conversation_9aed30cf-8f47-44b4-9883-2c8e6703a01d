#!/usr/bin/env python3
"""
PERFECT VOICE SYSTEM
Complete voice-to-voice AI with all issues fixed
"""

import sys
import os
import time
import threading
import numpy as np
from typing import Optional

# Add modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

# Import our enhanced modules
from clean_config import CleanVoiceAIConfig
from fast_ai_client import UltraFastAIClient
from amazing_tts import UltraPremiumTTS
from premium_audio_engine import PremiumAudioEngine

# Core imports
import whisper
import pyaudio
import webrtcvad
import warnings
warnings.filterwarnings("ignore")

class PerfectVoiceSystem:
    """Perfect voice-to-voice AI system with all issues resolved"""
    
    def __init__(self):
        print("🚀 PERFECT VOICE SYSTEM INITIALIZATION")
        print("=" * 60)
        
        # Load configuration
        self.config = CleanVoiceAIConfig()
        
        # Initialize components
        self._init_audio_system()
        self._init_voice_detection()
        self._init_speech_recognition()
        self._init_ai_client()
        self._init_text_to_speech()
        
        # System state
        self.is_listening = False
        self.is_processing = False
        self.conversation_active = False
        
        print("✅ PERFECT VOICE SYSTEM READY")
        print("=" * 60)
        
    def _init_audio_system(self):
        """Initialize premium audio system"""
        print("🎵 Initializing premium audio system...")
        
        try:
            # Initialize PyAudio for recording
            self.audio = pyaudio.PyAudio()
            
            # Find best audio device
            self._find_best_audio_device()
            
            # Initialize premium audio engine
            self.audio_engine = PremiumAudioEngine(self.config.sample_rate)
            
            print("✅ Premium audio system ready")
            
        except Exception as e:
            print(f"❌ Audio system initialization failed: {e}")
            raise
            
    def _find_best_audio_device(self):
        """Find the best audio input device"""
        print("🎤 Finding best audio device...")
        
        device_count = self.audio.get_device_count()
        best_device = None
        best_channels = 0
        
        for i in range(device_count):
            try:
                device_info = self.audio.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:
                    if device_info['maxInputChannels'] > best_channels:
                        best_device = i
                        best_channels = device_info['maxInputChannels']
                        
            except Exception:
                continue
                
        if best_device is not None:
            device_info = self.audio.get_device_info_by_index(best_device)
            self.input_device = best_device
            print(f"✅ Best audio device: {device_info['name']}")
            print(f"   Channels: {device_info['maxInputChannels']}")
            print(f"   Sample Rate: {device_info['defaultSampleRate']}")
        else:
            self.input_device = None
            print("⚠️ Using default audio device")
            
    def _init_voice_detection(self):
        """Initialize advanced voice activity detection"""
        print("🎯 Initializing advanced voice detection...")
        
        try:
            # Initialize WebRTC VAD with maximum sensitivity
            self.vad = webrtcvad.Vad(self.config.vad_aggressiveness)
            
            # Audio parameters for VAD
            self.vad_sample_rate = 16000  # WebRTC VAD requires 16kHz
            self.vad_frame_duration = 30  # 30ms frames
            self.vad_frame_size = int(self.vad_sample_rate * self.vad_frame_duration / 1000)
            
            print("✅ Advanced voice detection ready")
            print(f"   Aggressiveness: {self.config.vad_aggressiveness}/3")
            print(f"   Frame size: {self.vad_frame_size} samples")
            
        except Exception as e:
            print(f"❌ Voice detection initialization failed: {e}")
            raise
            
    def _init_speech_recognition(self):
        """Initialize high-performance speech recognition"""
        print("🎧 Initializing speech recognition...")
        
        try:
            # Load Whisper model with optimization
            print(f"Loading Whisper {self.config.whisper_model}...")
            self.whisper_model = whisper.load_model(
                self.config.whisper_model,
                device=self.config.whisper_device
            )
            
            print("✅ Speech recognition ready")
            print(f"   Model: Whisper {self.config.whisper_model}")
            print(f"   Device: {self.config.whisper_device}")
            
        except Exception as e:
            print(f"❌ Speech recognition initialization failed: {e}")
            raise
            
    def _init_ai_client(self):
        """Initialize ultra-fast AI client"""
        print("🤖 Initializing AI client...")
        
        try:
            self.ai_client = UltraFastAIClient(self.config)
            
            # Test AI connection
            if self.ai_client.test_ai_response():
                print("✅ AI client ready and tested")
            else:
                print("⚠️ AI client ready but connection issues detected")
                
        except Exception as e:
            print(f"❌ AI client initialization failed: {e}")
            raise
            
    def _init_text_to_speech(self):
        """Initialize ultra-premium text-to-speech"""
        print("🎵 Initializing text-to-speech...")
        
        try:
            self.tts = UltraPremiumTTS(self.config)
            
            # Test TTS
            if self.tts.test_voice():
                print("✅ Text-to-speech ready and tested")
            else:
                print("⚠️ Text-to-speech ready but test failed")
                
        except Exception as e:
            print(f"❌ Text-to-speech initialization failed: {e}")
            raise
            
    def start_conversation(self):
        """Start the perfect voice conversation"""
        print("\n🎙️ STARTING PERFECT VOICE CONVERSATION")
        print("=" * 50)
        print("Say something to begin...")
        print("Press Ctrl+C to stop")
        print("=" * 50)
        
        self.conversation_active = True
        
        try:
            while self.conversation_active:
                # Listen for speech
                audio_data = self._listen_for_speech()
                
                if audio_data is not None:
                    # Process the speech
                    self._process_speech(audio_data)
                    
        except KeyboardInterrupt:
            print("\n🛑 Conversation stopped by user")
        except Exception as e:
            print(f"\n❌ Conversation error: {e}")
        finally:
            self.stop_conversation()
            
    def _listen_for_speech(self) -> Optional[np.ndarray]:
        """Listen for speech with advanced detection"""
        
        if self.is_processing:
            return None
            
        try:
            # Start recording
            stream = self.audio.open(
                format=pyaudio.paInt16,
                channels=1,
                rate=self.vad_sample_rate,
                input=True,
                input_device_index=self.input_device,
                frames_per_buffer=self.vad_frame_size
            )
            
            print("🎤 Listening...")
            
            audio_frames = []
            silence_frames = 0
            speech_detected = False
            max_silence_frames = int(self.config.silence_duration_ms / self.vad_frame_duration)
            
            while self.conversation_active and not self.is_processing:
                # Read audio frame
                frame_data = stream.read(self.vad_frame_size, exception_on_overflow=False)
                
                # Check for voice activity
                is_speech = self.vad.is_speech(frame_data, self.vad_sample_rate)
                
                if is_speech:
                    speech_detected = True
                    silence_frames = 0
                    audio_frames.append(frame_data)
                    print("🗣️", end="", flush=True)
                elif speech_detected:
                    silence_frames += 1
                    audio_frames.append(frame_data)
                    
                    if silence_frames >= max_silence_frames:
                        print(" 🔄")
                        break
                        
                # Prevent infinite recording
                if len(audio_frames) > self.config.max_speech_duration_s * 1000 / self.vad_frame_duration:
                    print(" ⏰")
                    break
                    
            stream.stop_stream()
            stream.close()
            
            if speech_detected and audio_frames:
                # Convert to numpy array
                audio_data = np.frombuffer(b''.join(audio_frames), dtype=np.int16)
                audio_data = audio_data.astype(np.float32) / 32768.0
                
                # Enhance audio quality
                if self.audio_engine:
                    audio_data = self.audio_engine.enhance_input_audio(audio_data)
                    
                return audio_data
                
        except Exception as e:
            print(f"❌ Listening error: {e}")
            
        return None
        
    def _process_speech(self, audio_data: np.ndarray):
        """Process speech with ultra-fast pipeline"""
        
        self.is_processing = True
        
        try:
            print("🔄 Processing speech...")
            start_time = time.time()
            
            # Convert sample rate if needed
            if self.audio_engine and self.vad_sample_rate != self.config.sample_rate:
                audio_data = self.audio_engine.convert_sample_rate(
                    audio_data, self.vad_sample_rate, self.config.sample_rate
                )
                
            # Speech-to-text
            print("🎧 Transcribing...")
            result = self.whisper_model.transcribe(audio_data, language="en")
            text = result["text"].strip()
            
            if not text:
                print("⚠️ No speech detected")
                return
                
            print(f"📝 You said: '{text}'")
            
            # Get AI response
            print("🤖 Getting AI response...")
            ai_response = self.ai_client.get_response(text)
            
            if not ai_response:
                print("⚠️ No AI response")
                return
                
            print(f"🤖 AI: '{ai_response}'")
            
            # Text-to-speech
            print("🎵 Speaking response...")
            self.tts.speak(ai_response)
            
            duration = time.time() - start_time
            print(f"✅ Processing completed in {duration:.1f}s")
            
        except Exception as e:
            print(f"❌ Processing error: {e}")
        finally:
            self.is_processing = False
            
    def stop_conversation(self):
        """Stop the conversation and cleanup"""
        print("\n🧹 Cleaning up...")
        
        self.conversation_active = False
        
        try:
            if hasattr(self, 'audio'):
                self.audio.terminate()
            if hasattr(self, 'ai_client'):
                self.ai_client.cleanup()
            if hasattr(self, 'tts'):
                self.tts.cleanup()
                
            print("✅ Cleanup completed")
            
        except Exception as e:
            print(f"⚠️ Cleanup error: {e}")

def main():
    """Main function"""
    try:
        # Create and start the perfect voice system
        voice_system = PerfectVoiceSystem()
        voice_system.start_conversation()
        
    except Exception as e:
        print(f"❌ System error: {e}")
        return 1
        
    return 0

if __name__ == "__main__":
    exit(main())
