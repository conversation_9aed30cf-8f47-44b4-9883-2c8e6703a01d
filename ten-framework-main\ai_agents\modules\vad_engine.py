#!/usr/bin/env python3
"""
VAD Engine Module - Clean Voice Activity Detection
Handles HuBERT VAD and WebRTC VAD
"""

import torch
import numpy as np
import webrtcvad
import warnings
warnings.filterwarnings("ignore")

try:
    from transformers import AutoModel
    HUBERT_AVAILABLE = True
except ImportError:
    HUBERT_AVAILABLE = False

class VADEngine:
    """Clean VAD Engine with HuBERT and WebRTC"""
    
    def __init__(self, config):
        self.config = config
        
        # Initialize VAD systems
        self._init_hubert_vad()
        self._init_webrtc_vad()
        
    def _init_hubert_vad(self):
        """Initialize HuBERT VAD"""
        print("🔄 Initializing HuBERT VAD...")
        
        if self.config.use_hubert_vad and HUBERT_AVAILABLE:
            try:
                print(f"📱 Loading HuBERT: {self.config.hubert_vad_model}")
                
                self.hubert_vad = AutoModel.from_pretrained(
                    self.config.hubert_vad_model,
                    device_map=self.config.device,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
                )
                
                print(f"✅ HuBERT VAD ready on {self.config.device}")
                self.vad_mode = "hubert"
                return
                
            except Exception as e:
                print(f"❌ HuBERT VAD failed: {e}")
        
        print("🔄 Using WebRTC VAD fallback")
        self.vad_mode = "webrtc"
        self.hubert_vad = None
        
    def _init_webrtc_vad(self):
        """Initialize WebRTC VAD"""
        try:
            self.webrtc_vad = webrtcvad.Vad(self.config.vad_aggressiveness)
            print(f"✅ WebRTC VAD ready (level {self.config.vad_aggressiveness})")
        except Exception as e:
            print(f"❌ WebRTC VAD error: {e}")
            self.webrtc_vad = None
            
    def detect_voice(self, audio_chunk: np.ndarray) -> bool:
        """Main voice detection method"""
        try:
            if self.vad_mode == "hubert" and self.hubert_vad:
                return self._detect_hubert(audio_chunk)
            else:
                return self._detect_webrtc(audio_chunk)
        except Exception as e:
            print(f"⚠️ VAD error: {e}")
            return self._detect_energy(audio_chunk)
            
    def _detect_hubert(self, audio_chunk: np.ndarray) -> bool:
        """HuBERT voice detection"""
        try:
            # Convert to tensor
            audio_tensor = torch.from_numpy(audio_chunk.astype(np.float32))
            if torch.cuda.is_available():
                audio_tensor = audio_tensor.cuda()
            
            with torch.no_grad():
                # Normalize
                audio_tensor = audio_tensor / torch.max(torch.abs(audio_tensor))
                audio_tensor = audio_tensor.unsqueeze(0)
                
                # Get prediction
                outputs = self.hubert_vad(audio_tensor)
                
                if hasattr(outputs, 'last_hidden_state'):
                    voice_score = torch.mean(outputs.last_hidden_state).item()
                else:
                    voice_score = torch.mean(outputs[0]).item()
                
                return abs(voice_score) > self.config.voice_threshold
                
        except Exception as e:
            print(f"⚠️ HuBERT detection error: {e}")
            return self._detect_webrtc(audio_chunk)
            
    def _detect_webrtc(self, audio_chunk: np.ndarray) -> bool:
        """WebRTC voice detection"""
        try:
            if self.webrtc_vad and len(audio_chunk) >= 320:
                audio_bytes = audio_chunk[:320].tobytes()
                return self.webrtc_vad.is_speech(audio_bytes, self.config.sample_rate)
            else:
                return self._detect_energy(audio_chunk)
        except Exception as e:
            print(f"⚠️ WebRTC detection error: {e}")
            return self._detect_energy(audio_chunk)
            
    def _detect_energy(self, audio_chunk: np.ndarray) -> bool:
        """Energy-based voice detection fallback"""
        try:
            energy = np.sqrt(np.mean(audio_chunk**2))
            return energy > 0.005
        except:
            return False
            
    def get_status(self) -> str:
        """Get VAD status"""
        if self.vad_mode == "hubert":
            return "HuBERT VAD"
        elif self.vad_mode == "webrtc":
            return "WebRTC VAD"
        else:
            return "Energy VAD"
