"""
Local Whisper ASR Extension Addon

This addon registers the Whisper ASR Extension with TEN Framework
"""

from ten import Addon, register_addon_as_extension, TenEnv
from .extension import WhisperASRExtension


@register_addon_as_extension("whisper_asr_python")
class WhisperASRExtensionAddon(Addon):
    """Addon for Whisper ASR Extension"""
    
    def on_create_instance(self, ten_env: TenEnv, name: str, context) -> None:
        """Create extension instance"""
        ten_env.log_info(f"Creating Whisper ASR Extension instance: {name}")
        extension = WhisperASRExtension(name)
        ten_env.on_create_instance_done(extension, context)
