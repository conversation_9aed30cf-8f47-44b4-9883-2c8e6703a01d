# AgenticSeek + TEN Framework Local Configuration

# LOCAL LLM - Ollama
OPENAI_API_BASE=http://localhost:11434/v1
OPENAI_API_KEY=ollama
OPENAI_MODEL=deepseek-r1:14b
OPENAI_VENDOR=ollama

# LOCAL STT - Whisper
DEEPGRAM_API_KEY=local_whisper
STT_PROVIDER=whisper
WHISPER_MODEL=large-v3
WHISPER_DEVICE=cuda

# LOCAL TTS - Bark
ELEVENLABS_TTS_KEY=local_bark
TTS_PROVIDER=bark
BARK_VOICE=v2/en_speaker_6
BARK_DEVICE=cuda

# Voice Activity Detection
VAD_PROVIDER=silero
VAD_THRESHOLD=0.5

# AgenticSeek Integration
AGENTICSEEK_URL=http://localhost:8080
AGENTICSEEK_API_KEY=local
AGENTICSEEK_WORK_DIR=/tmp/agenticseek_bridge

# TEN Framework
TEN_LOG_LEVEL=3

# Agora (for WebRTC - can be left empty for local testing)
AGORA_APP_ID=
AGORA_APP_CERTIFICATE=
