#!/usr/bin/env python3
"""
VOICE CLONING SYSTEM - PREMIUM VOICE SYNTHESIS
Implements voice cloning with reference audio and speaker selection
"""

import os
import torch
import numpy as np
import librosa
import soundfile as sf
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings("ignore")

# Try to import voice cloning libraries
try:
    import ChatTTS
    CHATTTS_AVAILABLE = True
except ImportError:
    CHATTTS_AVAILABLE = False

try:
    from chatterbox.tts import ChatterboxTTS
    CHATTERBOX_AVAILABLE = True
except ImportError:
    CHATTERBOX_AVAILABLE = False

class VoiceCloningSystem:
    """Premium voice cloning and speaker selection system"""
    
    def __init__(self, config):
        self.config = config
        self.voice_profiles = {}
        self.current_speaker = None
        
        # Initialize voice cloning systems
        self._init_voice_systems()
        self._load_voice_profiles()
        
    def _init_voice_systems(self):
        """Initialize voice cloning systems"""
        print("🎭 INITIALIZING VOICE CLONING SYSTEMS")
        print("=" * 50)
        
        # Initialize ChatTTS for voice cloning
        if CHATTTS_AVAILABLE:
            try:
                print("📱 Loading ChatTTS Voice Cloning...")
                self.chat_tts = ChatTTS.Chat()
                self.chat_tts.load(compile=False, source="huggingface")
                print("✅ ChatTTS Voice Cloning ready")
                self.voice_cloning_available = True
            except Exception as e:
                print(f"❌ ChatTTS Voice Cloning failed: {e}")
                self.chat_tts = None
                self.voice_cloning_available = False
        else:
            self.chat_tts = None
            self.voice_cloning_available = False
            
        # Initialize ChatterboxTTS for voice cloning
        if CHATTERBOX_AVAILABLE:
            try:
                print("📱 Loading ChatterboxTTS Voice Cloning...")
                self.chatterbox_tts = ChatterboxTTS.from_pretrained(
                    device=self.config.device
                )
                print("✅ ChatterboxTTS Voice Cloning ready")
                if not self.voice_cloning_available:
                    self.voice_cloning_available = True
            except Exception as e:
                print(f"❌ ChatterboxTTS Voice Cloning failed: {e}")
                self.chatterbox_tts = None
        else:
            self.chatterbox_tts = None
            
        print(f"🎭 Voice Cloning Available: {self.voice_cloning_available}")
        print("=" * 50)
        
    def _load_voice_profiles(self):
        """Load predefined voice profiles"""
        # Premium voice profiles
        self.voice_profiles = {
            "aria": {
                "name": "Aria",
                "description": "Professional female voice",
                "type": "neural",
                "quality": "premium",
                "language": "en-US",
                "characteristics": ["clear", "professional", "warm"]
            },
            "jenny": {
                "name": "Jenny",
                "description": "Friendly conversational voice",
                "type": "neural", 
                "quality": "premium",
                "language": "en-US",
                "characteristics": ["friendly", "conversational", "natural"]
            },
            "guy": {
                "name": "Guy",
                "description": "Professional male voice",
                "type": "neural",
                "quality": "premium", 
                "language": "en-US",
                "characteristics": ["professional", "authoritative", "clear"]
            },
            "davis": {
                "name": "Davis",
                "description": "Casual male voice",
                "type": "neural",
                "quality": "premium",
                "language": "en-US", 
                "characteristics": ["casual", "friendly", "relaxed"]
            }
        }
        
        print(f"✅ Loaded {len(self.voice_profiles)} voice profiles")
        
    def clone_voice_from_audio(self, reference_audio_path: str, speaker_name: str) -> bool:
        """Clone voice from reference audio"""
        try:
            if not os.path.exists(reference_audio_path):
                print(f"❌ Reference audio not found: {reference_audio_path}")
                return False
                
            print(f"🎭 Cloning voice from: {reference_audio_path}")
            
            # Load reference audio
            audio_data, sample_rate = librosa.load(reference_audio_path, sr=22050)
            
            # Validate audio quality
            if len(audio_data) < 22050:  # Less than 1 second
                print("❌ Reference audio too short (minimum 1 second)")
                return False
                
            # Extract voice characteristics
            voice_features = self._extract_voice_features(audio_data, sample_rate)
            
            # Create voice profile
            voice_profile = {
                "name": speaker_name,
                "description": f"Cloned voice from {os.path.basename(reference_audio_path)}",
                "type": "cloned",
                "quality": "premium",
                "language": "en-US",
                "reference_audio": reference_audio_path,
                "features": voice_features,
                "characteristics": ["cloned", "custom"]
            }
            
            # Store voice profile
            self.voice_profiles[speaker_name.lower()] = voice_profile
            
            print(f"✅ Voice cloned successfully: {speaker_name}")
            return True
            
        except Exception as e:
            print(f"❌ Voice cloning failed: {e}")
            return False
            
    def _extract_voice_features(self, audio_data: np.ndarray, sample_rate: int) -> Dict:
        """Extract voice characteristics from audio"""
        try:
            features = {}
            
            # Extract fundamental frequency (pitch)
            f0 = librosa.yin(audio_data, fmin=50, fmax=400, sr=sample_rate)
            features['mean_f0'] = np.mean(f0[f0 > 0])
            features['std_f0'] = np.std(f0[f0 > 0])
            
            # Extract spectral features
            spectral_centroids = librosa.feature.spectral_centroid(y=audio_data, sr=sample_rate)[0]
            features['spectral_centroid'] = np.mean(spectral_centroids)
            
            # Extract MFCC features
            mfccs = librosa.feature.mfcc(y=audio_data, sr=sample_rate, n_mfcc=13)
            features['mfcc_mean'] = np.mean(mfccs, axis=1).tolist()
            features['mfcc_std'] = np.std(mfccs, axis=1).tolist()
            
            # Extract tempo and rhythm
            tempo, _ = librosa.beat.beat_track(y=audio_data, sr=sample_rate)
            features['tempo'] = float(tempo)
            
            # Extract energy
            rms = librosa.feature.rms(y=audio_data)[0]
            features['energy_mean'] = np.mean(rms)
            features['energy_std'] = np.std(rms)
            
            return features
            
        except Exception as e:
            print(f"⚠️ Feature extraction failed: {e}")
            return {}
            
    def select_speaker(self, speaker_name: str) -> bool:
        """Select a speaker for TTS"""
        speaker_key = speaker_name.lower()
        
        if speaker_key in self.voice_profiles:
            self.current_speaker = speaker_key
            profile = self.voice_profiles[speaker_key]
            print(f"🎭 Selected speaker: {profile['name']} ({profile['description']})")
            return True
        else:
            print(f"❌ Speaker not found: {speaker_name}")
            print(f"Available speakers: {list(self.voice_profiles.keys())}")
            return False
            
    def synthesize_with_voice(self, text: str, speaker_name: Optional[str] = None) -> Optional[np.ndarray]:
        """Synthesize speech with selected voice"""
        try:
            # Use specified speaker or current speaker
            if speaker_name:
                if not self.select_speaker(speaker_name):
                    return None
            elif not self.current_speaker:
                # Default to first available speaker
                if self.voice_profiles:
                    self.current_speaker = list(self.voice_profiles.keys())[0]
                else:
                    print("❌ No speakers available")
                    return None
                    
            profile = self.voice_profiles[self.current_speaker]
            print(f"🗣️ Synthesizing with {profile['name']}: {text[:50]}...")
            
            # Synthesize based on voice type
            if profile['type'] == 'cloned' and self.voice_cloning_available:
                return self._synthesize_cloned_voice(text, profile)
            elif self.voice_cloning_available:
                return self._synthesize_neural_voice(text, profile)
            else:
                print("❌ Voice synthesis not available")
                return None
                
        except Exception as e:
            print(f"❌ Voice synthesis failed: {e}")
            return None
            
    def _synthesize_cloned_voice(self, text: str, profile: Dict) -> Optional[np.ndarray]:
        """Synthesize speech with cloned voice"""
        try:
            if self.chat_tts and 'reference_audio' in profile:
                # Load reference audio for voice cloning
                ref_audio, _ = librosa.load(profile['reference_audio'], sr=22050)
                
                # Use ChatTTS with voice cloning
                texts = [text]
                
                # Generate with voice characteristics
                wavs = self.chat_tts.infer(
                    texts, 
                    use_decoder=True,
                    do_text_normalization=True
                )
                
                if wavs and len(wavs) > 0:
                    return wavs[0].astype(np.float32)
                    
            return None
            
        except Exception as e:
            print(f"❌ Cloned voice synthesis failed: {e}")
            return None
            
    def _synthesize_neural_voice(self, text: str, profile: Dict) -> Optional[np.ndarray]:
        """Synthesize speech with neural voice"""
        try:
            if self.chat_tts:
                # Use ChatTTS with speaker characteristics
                texts = [text]
                
                # Generate with neural voice
                wavs = self.chat_tts.infer(
                    texts,
                    use_decoder=True,
                    do_text_normalization=True
                )
                
                if wavs and len(wavs) > 0:
                    return wavs[0].astype(np.float32)
                    
            return None
            
        except Exception as e:
            print(f"❌ Neural voice synthesis failed: {e}")
            return None
            
    def get_available_speakers(self) -> List[Dict]:
        """Get list of available speakers"""
        return [
            {
                'id': key,
                'name': profile['name'],
                'description': profile['description'],
                'type': profile['type'],
                'quality': profile['quality'],
                'characteristics': profile['characteristics']
            }
            for key, profile in self.voice_profiles.items()
        ]
        
    def get_current_speaker(self) -> Optional[Dict]:
        """Get current speaker information"""
        if self.current_speaker and self.current_speaker in self.voice_profiles:
            profile = self.voice_profiles[self.current_speaker]
            return {
                'id': self.current_speaker,
                'name': profile['name'],
                'description': profile['description'],
                'type': profile['type'],
                'quality': profile['quality'],
                'characteristics': profile['characteristics']
            }
        return None
        
    def create_voice_sample(self, speaker_name: str, sample_text: str = "Hello, this is a voice sample.") -> Optional[str]:
        """Create a voice sample for testing"""
        try:
            audio_data = self.synthesize_with_voice(sample_text, speaker_name)
            
            if audio_data is not None:
                # Save sample
                sample_path = f"voice_samples/{speaker_name}_sample.wav"
                os.makedirs("voice_samples", exist_ok=True)
                
                sf.write(sample_path, audio_data, 22050)
                print(f"✅ Voice sample created: {sample_path}")
                return sample_path
            else:
                print(f"❌ Failed to create voice sample for {speaker_name}")
                return None
                
        except Exception as e:
            print(f"❌ Voice sample creation failed: {e}")
            return None
            
    def get_voice_cloning_status(self) -> Dict:
        """Get voice cloning system status"""
        return {
            'available': self.voice_cloning_available,
            'chattts_available': CHATTTS_AVAILABLE and self.chat_tts is not None,
            'chatterbox_available': CHATTERBOX_AVAILABLE and self.chatterbox_tts is not None,
            'total_voices': len(self.voice_profiles),
            'cloned_voices': len([p for p in self.voice_profiles.values() if p['type'] == 'cloned']),
            'current_speaker': self.current_speaker
        }
