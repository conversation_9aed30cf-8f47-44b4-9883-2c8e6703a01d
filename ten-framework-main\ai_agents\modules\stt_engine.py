#!/usr/bin/env python3
"""
STT Engine Module - Clean Speech-to-Text
Handles Whisper speech recognition
"""

import torch
import numpy as np
import warnings
warnings.filterwarnings("ignore")

try:
    import whisper
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False

class STTEngine:
    """Clean STT Engine with Whisper"""
    
    def __init__(self, config):
        self.config = config
        
        # Initialize STT
        self._init_whisper()
        
    def _init_whisper(self):
        """Initialize Whisper STT"""
        print("🔄 Initializing Whisper STT...")
        
        if WHISPER_AVAILABLE:
            try:
                print(f"📱 Loading Whisper: {self.config.whisper_model}")
                
                self.whisper_model = whisper.load_model(
                    self.config.whisper_model,
                    device=self.config.device
                )
                
                print(f"✅ Whisper ready on {self.config.device}")
                self.stt_available = True
                return
                
            except Exception as e:
                print(f"❌ Whisper failed: {e}")
        
        print("❌ No STT available")
        self.whisper_model = None
        self.stt_available = False
        
    def transcribe(self, audio_data: np.ndarray) -> str:
        """Main transcription method"""
        if not self.stt_available:
            return ""
            
        try:
            # Ensure audio is float32
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # Normalize audio
            if len(audio_data) > 0:
                audio_data = audio_data / np.max(np.abs(audio_data))
            
            # Transcribe with Whisper
            with torch.no_grad():
                result = self.whisper_model.transcribe(
                    audio_data,
                    language="en",
                    task="transcribe",
                    fp16=torch.cuda.is_available()
                )
                
                text = result.get("text", "").strip()
                
                if text:
                    print(f"🎤 Transcribed: {text}")
                    return text
                else:
                    return ""
                    
        except Exception as e:
            print(f"❌ Transcription error: {e}")
            return ""
            
    def is_available(self) -> bool:
        """Check if STT is available"""
        return self.stt_available
        
    def get_status(self) -> str:
        """Get STT status"""
        if self.stt_available:
            return f"Whisper {self.config.whisper_model}"
        else:
            return "STT Unavailable"
