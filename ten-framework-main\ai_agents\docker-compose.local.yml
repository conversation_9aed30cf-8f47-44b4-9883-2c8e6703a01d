version: '3.8'

services:
  # Ollama LLM Service (Local)
  ollama:
    image: ollama/ollama:latest
    container_name: ten_ollama
    restart: always
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_ORIGINS=*
    networks:
      - ten_agent_network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # TEN Framework Agent (Local Configuration)
  ten_agent_dev:
    image: docker.theten.ai/ten-framework/ten_agent_build:0.6.4
    container_name: ten_agent_dev
    platform: linux/amd64
    tty: true
    stdin_open: true
    restart: always
    ports:
      - "${GRAPH_DESIGNER_SERVER_PORT}:${GRAPH_DESIGNER_SERVER_PORT}"
      - "${SERVER_PORT}:${SERVER_PORT}"
    volumes:
      - ./:/app
      - ${LOG_PATH}:${LOG_PATH}
      - ../.vscode/:/app/.vscode
      - ../tools/pylint/.pylintrc:/tools/pylint/.pylintrc
      - ./models:/app/models  # Local voice models
      - ./data:/app/data      # Local data storage
    working_dir: /app
    env_file:
      - .env.local
    networks:
      - ten_agent_network
    depends_on:
      - ollama
      - agenticseek
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # AgenticSeek Research Service (Local)
  agenticseek:
    build:
      context: ../../agenticSeek-main
      dockerfile: Dockerfile
    container_name: ten_agenticseek
    restart: always
    ports:
      - "8080:8080"
      - "3001:3000"  # Web interface
    volumes:
      - ../../agenticSeek-main:/app
      - agenticseek_data:/app/data
    environment:
      - OLLAMA_URL=http://ollama:11434
      - LLM_PROVIDER=ollama
      - LLM_MODEL=deepseek-r1:14b
      - BROWSER_HEADLESS=true
      - LOCAL_MODE=true
    networks:
      - ten_agent_network
    depends_on:
      - ollama

  # TEN Framework Playground (Local)
  ten_agent_playground:
    image: ghcr.io/ten-framework/ten_agent_playground:0.10.11-8-g94c71358
    container_name: ten_agent_playground
    restart: always
    ports:
      - "3000:3000"
    networks:
      - ten_agent_network
    environment:
      - AGENT_SERVER_URL=http://ten_agent_dev:8080
      - TEN_DEV_SERVER_URL=http://ten_agent_dev:49483
      - LOCAL_MODE=true
    depends_on:
      - ten_agent_dev

  # TEN Framework Demo (Local)
  ten_agent_demo:
    image: ghcr.io/ten-framework/ten_agent_demo:0.10.6-19-g8ecacde4
    container_name: ten_agent_demo
    restart: always
    ports:
      - "3002:3000"
    networks:
      - ten_agent_network
    environment:
      - AGENT_SERVER_URL=http://ten_agent_dev:8080
      - LOCAL_MODE=true
    depends_on:
      - ten_agent_dev

  # Local WebRTC Server (Alternative to Agora)
  webrtc_server:
    image: janus-gateway/janus-gateway:latest
    container_name: ten_webrtc
    restart: always
    ports:
      - "8088:8088"  # HTTP
      - "8089:8089"  # HTTPS
      - "10000-10200:10000-10200/udp"  # RTP
    volumes:
      - ./webrtc_config:/opt/janus/etc/janus
    networks:
      - ten_agent_network
    environment:
      - JANUS_LOG_LEVEL=4

  # Local Vector Database (Alternative to cloud storage)
  vector_db:
    image: qdrant/qdrant:latest
    container_name: ten_vector_db
    restart: always
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - vector_data:/qdrant/storage
    networks:
      - ten_agent_network
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334

volumes:
  ollama_data:
    driver: local
  agenticseek_data:
    driver: local
  vector_data:
    driver: local

networks:
  ten_agent_network:
    driver: bridge
