"""
Local Bark TTS Extension for TEN Framework

This extension provides local text-to-speech using Bark
with CUDA acceleration for privacy-focused voice synthesis.
"""

import asyncio
import numpy as np
import os
import sys
import traceback
from typing import Optional

import torch
from bark import SAMPLE_RATE, generate_audio, preload_models
from bark.generation import set_seed

from ten import (
    AsyncExtension,
    AsyncTenEnv,
    Cmd,
    CmdResult,
    Data,
    StatusCode,
    TenError,
)


class BarkTTSExtension(AsyncExtension):
    """Local Bark TTS Extension"""
    
    def __init__(self, name: str) -> None:
        super().__init__(name)
        self.voice = "v2/en_speaker_6"
        self.device = "cuda"
        self.sample_rate = SAMPLE_RATE  # Bark's default sample rate (24000)
        self.channels = 1
        self.chunk_size = 1024
        self.enable_offload = True
        self.text_temp = 0.7
        self.waveform_temp = 0.7
        self.models_loaded = False
        
    async def on_init(self, ten_env: AsyncTenEnv) -> None:
        """Initialize the Bark TTS extension"""
        ten_env.log_info("Bark TTS Extension initializing...")
        
        # Load configuration
        self.voice = ten_env.get_property_string("voice") or self.voice
        self.device = ten_env.get_property_string("device") or self.device
        self.sample_rate = ten_env.get_property_int("sample_rate") or self.sample_rate
        self.channels = ten_env.get_property_int("channels") or self.channels
        self.chunk_size = ten_env.get_property_int("chunk_size") or self.chunk_size
        self.enable_offload = ten_env.get_property_bool("enable_offload") or self.enable_offload
        self.text_temp = ten_env.get_property_float("text_temp") or self.text_temp
        self.waveform_temp = ten_env.get_property_float("waveform_temp") or self.waveform_temp
        
        # Check CUDA availability
        if self.device == "cuda" and not torch.cuda.is_available():
            ten_env.log_warn("CUDA not available, falling back to CPU")
            self.device = "cpu"
            
        ten_env.log_info(f"Using device: {self.device}")
        
        # Set device for Bark
        os.environ["SUNO_USE_SMALL_MODELS"] = "False"
        if self.device == "cuda":
            os.environ["SUNO_OFFLOAD_CPU"] = "False" if not self.enable_offload else "True"
        else:
            os.environ["SUNO_OFFLOAD_CPU"] = "True"
            
        # Preload Bark models
        try:
            ten_env.log_info("Loading Bark models...")
            await asyncio.get_event_loop().run_in_executor(None, preload_models)
            self.models_loaded = True
            ten_env.log_info("Bark models loaded successfully")
        except Exception as e:
            ten_env.log_error(f"Failed to load Bark models: {str(e)}")
            raise TenError(f"Bark model loading failed: {str(e)}")
            
    async def on_deinit(self, ten_env: AsyncTenEnv) -> None:
        """Cleanup resources"""
        ten_env.log_info("Bark TTS Extension deinitialized")
        
    async def on_cmd(self, ten_env: AsyncTenEnv, cmd: Cmd) -> None:
        """Handle incoming commands"""
        cmd_name = cmd.get_name()
        ten_env.log_info(f"Received command: {cmd_name}")
        
        try:
            if cmd_name == "text_data":
                await self._handle_text_to_speech(ten_env, cmd)
            else:
                ten_env.log_warn(f"Unknown command: {cmd_name}")
                await ten_env.return_result(CmdResult.create(StatusCode.ERROR), cmd)
                
        except Exception as e:
            ten_env.log_error(f"Error handling command {cmd_name}: {str(e)}")
            ten_env.log_error(traceback.format_exc())
            await ten_env.return_result(CmdResult.create(StatusCode.ERROR), cmd)
            
    async def _handle_text_to_speech(self, ten_env: AsyncTenEnv, cmd: Cmd) -> None:
        """Handle text-to-speech conversion"""
        if not self.models_loaded:
            ten_env.log_error("Bark models not loaded")
            await ten_env.return_result(CmdResult.create(StatusCode.ERROR), cmd)
            return
            
        text = cmd.get_property_string("text")
        if not text or not text.strip():
            ten_env.log_warn("No text provided for TTS")
            await ten_env.return_result(CmdResult.create(StatusCode.ERROR), cmd)
            return
            
        ten_env.log_info(f"Generating speech for: {text[:50]}...")
        
        try:
            # Generate audio with Bark
            audio_array = await asyncio.get_event_loop().run_in_executor(
                None, self._generate_speech, text
            )
            
            if audio_array is not None:
                # Send audio data
                await self._send_audio_data(ten_env, audio_array)
                await ten_env.return_result(CmdResult.create(StatusCode.OK), cmd)
            else:
                await ten_env.return_result(CmdResult.create(StatusCode.ERROR), cmd)
                
        except Exception as e:
            ten_env.log_error(f"Error in text-to-speech: {str(e)}")
            ten_env.log_error(traceback.format_exc())
            await ten_env.return_result(CmdResult.create(StatusCode.ERROR), cmd)
            
    def _generate_speech(self, text: str) -> Optional[np.ndarray]:
        """Generate speech using Bark (runs in thread pool)"""
        try:
            # Set random seed for reproducibility
            set_seed(42)
            
            # Generate audio with Bark
            audio_array = generate_audio(
                text,
                history_prompt=self.voice,
                text_temp=self.text_temp,
                waveform_temp=self.waveform_temp
            )
            
            # Ensure audio is float32 and normalized
            if audio_array.dtype != np.float32:
                audio_array = audio_array.astype(np.float32)
                
            # Normalize to [-1, 1] range
            max_val = np.max(np.abs(audio_array))
            if max_val > 0:
                audio_array = audio_array / max_val
                
            return audio_array
            
        except Exception as e:
            print(f"Bark TTS generation error: {str(e)}")
            return None
            
    async def _send_audio_data(self, ten_env: AsyncTenEnv, audio_array: np.ndarray) -> None:
        """Send audio data as PCM frames"""
        try:
            # Convert float32 to int16 for PCM
            audio_int16 = (audio_array * 32767).astype(np.int16)
            
            # Send audio in chunks
            total_samples = len(audio_int16)
            bytes_per_sample = 2  # int16
            
            for i in range(0, total_samples, self.chunk_size):
                chunk = audio_int16[i:i + self.chunk_size]
                chunk_bytes = chunk.tobytes()
                
                # Create PCM frame data
                pcm_data = Data.create("pcm_frame")
                pcm_data.set_property_int("sample_rate", self.sample_rate)
                pcm_data.set_property_int("bytes_per_sample", bytes_per_sample)
                pcm_data.set_property_int("number_of_channels", self.channels)
                pcm_data.set_property_string("data_fmt", "pcm")
                pcm_data.set_property_int("samples_per_channel", len(chunk))
                
                # Set audio data
                pcm_data.set_buf(chunk_bytes)
                
                # Send data
                await ten_env.send_data(pcm_data)
                
            ten_env.log_info(f"Audio sent: {total_samples} samples, {total_samples / self.sample_rate:.2f}s")
            
        except Exception as e:
            ten_env.log_error(f"Error sending audio data: {str(e)}")
            ten_env.log_error(traceback.format_exc())
