{"type": "extension", "name": "bark_tts_python", "version": "0.1.0", "language": "python", "dependencies": [{"type": "system", "name": "ten_runtime_python", "version": "0.6.4"}], "api": {"cmd_in": [{"name": "text_data", "property": {"text": {"type": "string"}}}], "data_out": [{"name": "pcm_frame", "property": {"sample_rate": {"type": "int64"}, "bytes_per_sample": {"type": "int64"}, "number_of_channels": {"type": "int64"}, "data_fmt": {"type": "string"}, "samples_per_channel": {"type": "int64"}}}]}}