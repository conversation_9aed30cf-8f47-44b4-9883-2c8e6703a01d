"""
Local Whisper ASR Extension for TEN Framework

This extension provides local speech-to-text using OpenAI Whisper
with CUDA acceleration for privacy-focused voice processing.
"""

import asyncio
import io
import numpy as np
import os
import sys
import threading
import time
import traceback
import wave
from typing import Optional

import librosa
import soundfile as sf
import torch
import whisper

from ten import (
    AsyncExtension,
    AsyncTenEnv,
    Data,
    TenError,
)


class WhisperASRExtension(AsyncExtension):
    """Local Whisper ASR Extension"""
    
    def __init__(self, name: str) -> None:
        super().__init__(name)
        self.model: Optional[whisper.Whisper] = None
        self.model_name = "large-v3"
        self.device = "cuda"
        self.language = "en"
        self.chunk_duration = 5.0
        self.vad_threshold = 0.5
        self.enable_vad = True
        self.sample_rate = 16000
        self.channels = 1
        
        # Audio buffering
        self.audio_buffer = []
        self.buffer_lock = threading.Lock()
        self.last_transcription_time = 0
        self.transcription_interval = 1.0  # seconds
        
    async def on_init(self, ten_env: AsyncTenEnv) -> None:
        """Initialize the Whisper ASR extension"""
        ten_env.log_info("Whisper ASR Extension initializing...")
        
        # Load configuration
        self.model_name = ten_env.get_property_string("model") or self.model_name
        self.device = ten_env.get_property_string("device") or self.device
        self.language = ten_env.get_property_string("language") or self.language
        self.chunk_duration = ten_env.get_property_float("chunk_duration") or self.chunk_duration
        self.vad_threshold = ten_env.get_property_float("vad_threshold") or self.vad_threshold
        self.enable_vad = ten_env.get_property_bool("enable_vad") or self.enable_vad
        self.sample_rate = ten_env.get_property_int("sample_rate") or self.sample_rate
        self.channels = ten_env.get_property_int("channels") or self.channels
        
        # Check CUDA availability
        if self.device == "cuda" and not torch.cuda.is_available():
            ten_env.log_warn("CUDA not available, falling back to CPU")
            self.device = "cpu"
            
        ten_env.log_info(f"Using device: {self.device}")
        
        # Load Whisper model
        try:
            ten_env.log_info(f"Loading Whisper model: {self.model_name}")
            self.model = whisper.load_model(self.model_name, device=self.device)
            ten_env.log_info("Whisper model loaded successfully")
        except Exception as e:
            ten_env.log_error(f"Failed to load Whisper model: {str(e)}")
            raise TenError(f"Whisper model loading failed: {str(e)}")
            
    async def on_deinit(self, ten_env: AsyncTenEnv) -> None:
        """Cleanup resources"""
        if self.model:
            del self.model
            self.model = None
        ten_env.log_info("Whisper ASR Extension deinitialized")
        
    async def on_data(self, ten_env: AsyncTenEnv, data: Data) -> None:
        """Handle incoming audio data"""
        data_name = data.get_name()
        
        if data_name == "pcm_frame":
            await self._handle_pcm_frame(ten_env, data)
        else:
            ten_env.log_warn(f"Unknown data type: {data_name}")
            
    async def _handle_pcm_frame(self, ten_env: AsyncTenEnv, data: Data) -> None:
        """Handle PCM audio frame"""
        try:
            # Get audio properties
            sample_rate = data.get_property_int("sample_rate")
            bytes_per_sample = data.get_property_int("bytes_per_sample")
            number_of_channels = data.get_property_int("number_of_channels")
            samples_per_channel = data.get_property_int("samples_per_channel")
            
            # Get audio data
            audio_data = data.get_buf()
            if not audio_data:
                return
                
            # Convert bytes to numpy array
            if bytes_per_sample == 2:
                audio_array = np.frombuffer(audio_data, dtype=np.int16)
            elif bytes_per_sample == 4:
                audio_array = np.frombuffer(audio_data, dtype=np.int32)
            else:
                ten_env.log_error(f"Unsupported bytes_per_sample: {bytes_per_sample}")
                return
                
            # Convert to float32 and normalize
            audio_float = audio_array.astype(np.float32)
            if bytes_per_sample == 2:
                audio_float /= 32768.0
            elif bytes_per_sample == 4:
                audio_float /= 2147483648.0
                
            # Handle multi-channel audio (convert to mono)
            if number_of_channels > 1:
                audio_float = audio_float.reshape(-1, number_of_channels)
                audio_float = np.mean(audio_float, axis=1)
                
            # Resample if necessary
            if sample_rate != self.sample_rate:
                audio_float = librosa.resample(
                    audio_float, 
                    orig_sr=sample_rate, 
                    target_sr=self.sample_rate
                )
                
            # Add to buffer
            with self.buffer_lock:
                self.audio_buffer.extend(audio_float)
                
            # Check if we should transcribe
            current_time = time.time()
            if (current_time - self.last_transcription_time) >= self.transcription_interval:
                await self._transcribe_buffer(ten_env)
                self.last_transcription_time = current_time
                
        except Exception as e:
            ten_env.log_error(f"Error handling PCM frame: {str(e)}")
            ten_env.log_error(traceback.format_exc())

    async def _transcribe_buffer(self, ten_env: AsyncTenEnv) -> None:
        """Transcribe accumulated audio buffer"""
        if not self.model:
            return

        try:
            # Get audio data from buffer
            with self.buffer_lock:
                if len(self.audio_buffer) < self.sample_rate * 0.5:  # Less than 0.5 seconds
                    return

                # Get chunk for transcription
                chunk_samples = int(self.sample_rate * self.chunk_duration)
                if len(self.audio_buffer) >= chunk_samples:
                    audio_chunk = np.array(self.audio_buffer[:chunk_samples])
                    self.audio_buffer = self.audio_buffer[chunk_samples//2:]  # 50% overlap
                else:
                    audio_chunk = np.array(self.audio_buffer)
                    self.audio_buffer = []

            # Apply VAD if enabled
            if self.enable_vad:
                energy = np.mean(audio_chunk ** 2)
                if energy < self.vad_threshold:
                    return  # No speech detected

            # Transcribe with Whisper
            result = await asyncio.get_event_loop().run_in_executor(
                None, self._transcribe_audio, audio_chunk
            )

            if result and result.strip():
                # Send transcription result
                await self._send_transcription(ten_env, result, is_final=True)

        except Exception as e:
            ten_env.log_error(f"Error transcribing buffer: {str(e)}")
            ten_env.log_error(traceback.format_exc())

    def _transcribe_audio(self, audio_data: np.ndarray) -> str:
        """Transcribe audio using Whisper (runs in thread pool)"""
        try:
            # Ensure audio is the right shape and type
            if len(audio_data.shape) > 1:
                audio_data = audio_data.flatten()

            # Transcribe with Whisper
            result = self.model.transcribe(
                audio_data,
                language=self.language if self.language != "auto" else None,
                fp16=self.device == "cuda"
            )

            return result["text"].strip()

        except Exception as e:
            print(f"Whisper transcription error: {str(e)}")
            return ""

    async def _send_transcription(self, ten_env: AsyncTenEnv, text: str, is_final: bool = True) -> None:
        """Send transcription result"""
        try:
            # Create text data
            text_data = Data.create("text_data")
            text_data.set_property_string("text", text)
            text_data.set_property_bool("is_final", is_final)
            text_data.set_property_int("stream_id", 0)
            text_data.set_property_bool("end_of_segment", is_final)

            # Send data
            await ten_env.send_data(text_data)
            ten_env.log_info(f"Transcription sent: {text[:50]}...")

        except Exception as e:
            ten_env.log_error(f"Error sending transcription: {str(e)}")
