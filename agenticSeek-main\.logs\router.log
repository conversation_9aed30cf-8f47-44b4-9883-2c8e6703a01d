2025-06-29 18:14:51,148 - router.log - INFO - Routing Vote for text $env:WORK_DIR="C:\Users\<USER>\Downloads\labbb\agenticSeek-main\work_dir"; py -3.12 cli.py --query "Hello, test response" --no-browser: BART: code (0.*****************) LLM-router: files (0.****************)
2025-06-29 18:29:38,248 - router.log - INFO - Routing Vote for text Remove-Item -Path "sources\tools\__pycache__" -Recurse -Force -ErrorAction SilentlyContinue: BART: code (0.****************) LLM-router: files (0.****************)
2025-06-29 18:35:37,341 - router.log - INFO - Routing Vote for text Remove-Item -Path "sources\tools\__pycache__" -Recurse -Force -ErrorAction SilentlyContinue: BART: code (0.****************) LLM-router: files (0.****************)
2025-06-29 18:40:26,651 - router.log - INFO - Routing Vote for text Get-ChildItem -Path . -Recurse -Name "*.pyc" | Remove-Item -Force; Get-ChildItem -Path . -Recurse -Name "__pycache__" | Remove-Item -Recurse -Force: BART: code (0.****************) LLM-router: files (0.****************)
2025-06-29 18:59:54,897 - router.log - INFO - Routing Vote for text $env:WORK_DIR="C:\Users\<USER>\Downloads\labbb\agenticSeek-main\work_dir"; python cli.py --query "What is 2+2? Just give me a simple answer." --no-browser: BART: code (0.****************) LLM-router: files (0.****************)
2025-06-29 19:13:32,003 - router.log - INFO - Routing Vote for text What is 15 * 23?: BART: talk (0.***************) LLM-router: talk (0.***************)
2025-06-29 19:13:54,836 - router.log - INFO - Routing Vote for text What's the weather like today?: BART: talk (0.***************) LLM-router: talk (0.***************)
2025-06-29 19:47:28,326 - router.log - INFO - Routing Vote for text What is 2+2?: BART: talk (0.*****************) LLM-router: talk (0.****************)
2025-06-29 19:54:14,446 - router.log - INFO - Routing Vote for text Yeah, I don't know. But it sounds easy as shit.: BART: talk (0.***************) LLM-router: talk (0.*****************)
2025-06-29 19:55:23,566 - router.log - INFO - Routing Vote for text Shhh were you though? Aye 1 The russet?! I'm going to graze another away I'm going instead see a flat You can be part of this I actually go everywhere Oh Mysterious So we have a creek Let's see what we can do: BART: talk (0.****************) LLM-router: talk (0.****************)
2025-06-29 19:55:44,222 - router.log - INFO - Routing Vote for text Hello, hello, hello.: BART: talk (0.****************) LLM-router: talk (0.*****************)
2025-06-29 19:56:22,847 - router.log - INFO - Routing Vote for text What is 2 plus 2?: BART: planification (0.*****************) LLM-router: talk (0.****************)
2025-06-29 19:57:32,171 - router.log - INFO - Routing Vote for text Tell me about artificial intelligence: BART: talk (0.****************) LLM-router: talk (0.*****************)
2025-06-29 19:57:47,191 - router.log - INFO - Routing Vote for text What's the weather like today?: BART: talk (0.****************) LLM-router: talk (0.*****************)
2025-06-29 19:58:48,772 - router.log - INFO - Routing Vote for text What is 2 plus 2?: BART: planification (0.*****************) LLM-router: talk (0.****************)
2025-06-29 19:59:00,692 - router.log - INFO - Routing Vote for text Tell me about artificial intelligence: BART: talk (0.****************) LLM-router: talk (0.*****************)
2025-06-29 19:59:14,698 - router.log - INFO - Routing Vote for text What's the weather like today?: BART: talk (0.****************) LLM-router: talk (0.*****************)
2025-06-29 20:04:09,301 - router.log - INFO - Routing Vote for text Ah! Ah! Ah! I'm not going to do that. Hello? Hello, I'm...: BART: talk (0.****************) LLM-router: talk (0.*****************)
2025-06-29 20:04:47,488 - router.log - INFO - Routing Vote for text Hello, E, A, or what's it for you? Who saw? Finally, what's it going to do?: BART: talk (0.****************) LLM-router: talk (0.*****************)
2025-06-29 20:07:14,571 - router.log - INFO - Routing Vote for text Hi, my name is Iber Hinsamon. How are you?: BART: talk (0.****************) LLM-router: talk (0.*****************)
2025-06-29 20:07:40,729 - router.log - INFO - Routing Vote for text I'm good, how about you?: BART: talk (0.****************) LLM-router: talk (0.****************)
2025-06-29 20:08:08,732 - router.log - INFO - Routing Vote for text How are you? What are your agentate capabilities?: BART: talk (0.****************) LLM-router: talk (0.****************)
2025-06-29 20:08:37,509 - router.log - INFO - Routing Vote for text How are you?: BART: talk (0.****************) LLM-router: talk (0.****************)
2025-06-29 20:09:04,299 - router.log - INFO - Routing Vote for text Doing good, how are you?: BART: talk (0.****************) LLM-router: talk (0.****************)
2025-06-29 20:09:38,320 - router.log - INFO - Routing Vote for text I'm where are you trying to keep the ability what features you have: BART: talk (0.****************) LLM-router: talk (0.*****************)
2025-06-29 20:10:09,407 - router.log - INFO - Routing Vote for text You're very very very weird.: BART: files (0.*****************) LLM-router: talk (0.****************)
2025-06-29 20:10:37,727 - router.log - INFO - Routing Vote for text What are your capabilities and features? Do you have tools that you could do for me?: BART: talk (0.****************) LLM-router: talk (0.*****************)
2025-06-29 20:11:13,046 - router.log - INFO - Routing Vote for text Speak for us, Steve.: BART: talk (0.****************) LLM-router: talk (0.****************)
2025-06-29 20:16:06,229 - router.log - INFO - Routing Vote for text It's not working, you know, I cannot interrupt you at any time.: BART: talk (0.****************) LLM-router: talk (0.****************)
2025-06-29 20:30:03,977 - router.log - INFO - Routing Vote for text Hello. [User seems neutral]: BART: talk (0.****************) LLM-router: files (0.*****************)
2025-06-29 20:30:25,943 - router.log - INFO - Routing Vote for text Conf. [User seems neutral]: BART: talk (0.****************) LLM-router: files (0.****************)
2025-06-29 20:30:40,688 - router.log - INFO - Routing Vote for text I don't hear anything. [User seems neutral]: BART: web (0.****************) LLM-router: files (0.****************)
2025-06-29 20:31:14,046 - router.log - INFO - Routing Vote for text Is it a second or smoother? [User seems neutral]: BART: talk (0.***************) LLM-router: talk (0.*****************)
2025-06-29 20:32:04,221 - router.log - INFO - Routing Vote for text away. [User seems neutral]: BART: talk (0.***************) LLM-router: files (0.*****************)
2025-06-29 20:32:16,324 - router.log - INFO - Routing Vote for text I don't know what to do with this. Eh eh eh, don't touch it! [User seems neutral]: BART: talk (0.****************) LLM-router: talk (0.*****************)
2025-06-29 20:32:36,743 - router.log - INFO - Routing Vote for text Let me smack you one four of my power and then I'll be equal. [User seems neutral]: BART: talk (0.****************) LLM-router: files (0.*****************)
2025-06-29 20:33:16,484 - router.log - INFO - Routing Vote for text Hello [User seems neutral]: BART: talk (0.****************) LLM-router: files (0.****************)
2025-06-29 20:33:33,362 - router.log - INFO - Routing Vote for text I'm going to do a little bit of the [User seems neutral]: BART: talk (0.****************) LLM-router: files (0.*****************)
2025-06-29 20:34:28,708 - router.log - INFO - Routing Vote for text I'm going to put this on the top of the head. I'm going to put this on the top of the head. I'm going to put this on the top of the head. I'm going to put this on the top of the head. I'm going to put this on the top of the head. I'm going to put this on the top of the head. I'm going to put this on the top of the head. I'm going to put this on the top of the head. I'm going to put this on the top of the head. I'm going to put this on the top of the head. I'm going to put this on the top of the head. I'm going to put this on the top of the head. I'm going to put this on the top of the head. I'm going to put this on the top of the head. [User seems neutral]: BART: talk (0.****************) LLM-router: files (0.****************)
2025-06-29 20:44:54,856 - router.log - INFO - Routing Vote for text Hello? [User seems neutral]: BART: talk (0.****************) LLM-router: files (0.****************)
2025-06-29 20:45:12,227 - router.log - INFO - Routing Vote for text I cannot hear anything. [User seems neutral]: BART: web (0.****************) LLM-router: talk (0.****************)
2025-06-29 20:46:00,066 - router.log - INFO - Routing Vote for text I cannot hear anything. That's a problem. [User seems neutral]: BART: talk (0.****************) LLM-router: files (0.****************)
2025-06-29 20:46:55,645 - router.log - INFO - Routing Vote for text I'm going to get a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a little bit of a [User seems neutral]: BART: talk (0.****************) LLM-router: files (0.****************)
2025-06-29 21:03:31,065 - router.log - INFO - Routing Vote for text This is not good, bro. This is horrible.: BART: web (0.*****************) LLM-router: talk (0.****************)
2025-06-29 21:09:54,924 - router.log - INFO - Routing Vote for text Thank you.: BART: web (0.*****************) LLM-router: talk (0.****************)
2025-06-29 23:11:04,318 - router.log - INFO - Routing Vote for text Hello, can you hear me?: BART: talk (0.****************) LLM-router: talk (0.****************)
2025-06-29 23:11:46,533 - router.log - INFO - Routing Vote for text can you hear me?: BART: talk (0.***************) LLM-router: talk (0.***************)
2025-06-29 23:12:29,614 - router.log - INFO - Routing Vote for text Thank you.: BART: web (0.****************) LLM-router: talk (0.****************)
2025-06-29 23:30:53,083 - router.log - INFO - Routing Vote for text Thank you.: BART: web (0.*****************) LLM-router: talk (0.****************)
2025-06-29 23:32:32,881 - router.log - INFO - Routing Vote for text say thank you. I did not: BART: talk (0.***************) LLM-router: talk (0.*****************)
