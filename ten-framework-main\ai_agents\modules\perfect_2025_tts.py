#!/usr/bin/env python3
"""
PERFECT 2025 TEXT-TO-SPEECH ENGINE
Latest Coqui TTS models with crystal-clear voice quality and interruption support
"""

import torch
import numpy as np
import soundfile as sf
import tempfile
import os
import subprocess
import threading
from typing import Optional, Dict, Any
from concurrent.futures import Thread<PERSON>oolExecutor
try:
    from TTS.api import TTS
    COQUI_AVAILABLE = True
except ImportError:
    COQUI_AVAILABLE = False
    print("⚠️ Coqui TTS not available, using fallback TTS")

try:
    import edge_tts
    import asyncio
    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False
    print("⚠️ Edge TTS not available, using system TTS")

class Perfect2025TTS:
    """Perfect 2025 Text-to-Speech engine with latest models and interruption support"""
    
    def __init__(self, config):
        self.config = config
        self.device = config.device
        self.tts_model = None
        self.edge_tts_voice = "en-US-AriaNeural"  # High quality voice
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.current_playback = None
        self.is_speaking = False
        self._init_tts_models()
        
    def _init_tts_models(self):
        """Initialize 2025 TTS models with fallbacks"""
        try:
            if COQUI_AVAILABLE:
                print(f"🔊 Loading 2025 Coqui TTS model: {self.config.tts_model}...")

                # Initialize Coqui TTS with latest model
                self.tts_model = TTS(
                    model_name=self.config.tts_model,
                    progress_bar=False,
                    gpu=self.device == "cuda"
                )
                print("✅ Coqui TTS 2025 model loaded successfully")
            else:
                print("🔄 Coqui TTS not available, using system TTS...")
                self.tts_model = None

        except Exception as e:
            print(f"❌ Failed to load Coqui TTS: {e}")
            print("🔄 Using fallback TTS...")
            self.tts_model = None
    
    def speak(self, text: str, interrupt_previous: bool = True) -> bool:
        """Speak text with interruption support"""
        try:
            if not text or not text.strip():
                return False
            
            # Clean text for better TTS
            text = self._clean_text_for_tts(text)
            
            # Handle interruption
            if interrupt_previous and self.is_speaking:
                self.interrupt_speech()
            
            # Generate and play speech
            if self.config.parallel_processing:
                # Async generation for faster response
                future = self.executor.submit(self._generate_and_play, text)
                return True
            else:
                return self._generate_and_play(text)
                
        except Exception as e:
            print(f"❌ 2025 TTS Speak Error: {e}")
            return False
    
    def _generate_and_play(self, text: str) -> bool:
        """Generate and play speech"""
        try:
            self.is_speaking = True
            
            if self.tts_model:
                # Use Coqui TTS for high quality
                audio_data = self._generate_coqui_speech(text)
                if audio_data is not None:
                    self._play_audio(audio_data)
                    return True
            
            # Fallback to system TTS
            return self._generate_system_speech(text)
            
        except Exception as e:
            print(f"❌ Generate and Play Error: {e}")
            return False
        finally:
            self.is_speaking = False
    
    def _generate_coqui_speech(self, text: str) -> Optional[np.ndarray]:
        """Generate speech using Coqui TTS"""
        try:
            # Generate audio with Coqui TTS
            audio_data = self.tts_model.tts(text=text)
            
            # Convert to numpy array if needed
            if isinstance(audio_data, list):
                audio_data = np.array(audio_data, dtype=np.float32)
            
            # Ensure proper format
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # Normalize audio
            if np.max(np.abs(audio_data)) > 0:
                audio_data = audio_data / np.max(np.abs(audio_data)) * 0.8
            
            print(f"🔊 Coqui TTS generated {len(audio_data)} samples")
            return audio_data
            
        except Exception as e:
            print(f"❌ Coqui TTS Generation Error: {e}")
            return None
    
    def _generate_edge_speech(self, text: str) -> bool:
        """Generate speech using Edge TTS (fallback)"""
        try:
            if not EDGE_TTS_AVAILABLE:
                return self._generate_system_speech(text)

            async def _edge_tts_generate():
                communicate = edge_tts.Communicate(text, self.edge_tts_voice)

                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                    await communicate.save(tmp_file.name)
                    return tmp_file.name

            # Run async Edge TTS
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            audio_file = loop.run_until_complete(_edge_tts_generate())
            loop.close()

            # Play the audio file
            self._play_audio_file(audio_file)

            # Clean up
            os.unlink(audio_file)

            print(f"🔊 Edge TTS generated speech for: '{text[:50]}...'")
            return True

        except Exception as e:
            print(f"❌ Edge TTS Generation Error: {e}")
            return self._generate_system_speech(text)

    def _generate_system_speech(self, text: str) -> bool:
        """Generate speech using Windows system TTS (final fallback)"""
        try:
            # Clean text for PowerShell
            clean_text = self._clean_text_for_tts(text)
            clean_text = clean_text.replace("'", "''")  # Escape single quotes for PowerShell

            # Use Windows SAPI TTS
            cmd = f'Add-Type -AssemblyName System.Speech; $synth = New-Object System.Speech.Synthesis.SpeechSynthesizer; $synth.SelectVoice("Microsoft Zira Desktop"); $synth.Speak("{clean_text}")'

            result = subprocess.run(
                ['powershell', '-Command', cmd],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                print(f"🔊 System TTS generated speech for: '{text[:50]}...'")
                return True
            else:
                print(f"❌ System TTS Error: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ System TTS Generation Error: {e}")
            return False
    
    def _play_audio(self, audio_data: np.ndarray):
        """Play audio data"""
        try:
            # Save to temporary file and play
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                sf.write(tmp_file.name, audio_data, self.config.sample_rate)
                self._play_audio_file(tmp_file.name)
                os.unlink(tmp_file.name)
                
        except Exception as e:
            print(f"❌ Audio Playback Error: {e}")
    
    def _play_audio_file(self, audio_file: str):
        """Play audio file using system player"""
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(['powershell', '-c', f'(New-Object Media.SoundPlayer "{audio_file}").PlaySync()'], 
                             check=True, capture_output=True)
            else:  # Linux/Mac
                subprocess.run(['aplay' if os.path.exists('/usr/bin/aplay') else 'afplay', audio_file], 
                             check=True, capture_output=True)
                
        except Exception as e:
            print(f"❌ Audio File Playback Error: {e}")
    
    def interrupt_speech(self):
        """Interrupt current speech for real-time conversation"""
        try:
            if self.is_speaking:
                print("⚡ Interrupting current speech...")
                self.is_speaking = False
                
                # Kill any running audio processes
                if os.name == 'nt':  # Windows
                    subprocess.run(['taskkill', '/f', '/im', 'powershell.exe'], 
                                 capture_output=True, check=False)
                
                print("✅ Speech interrupted successfully")
                
        except Exception as e:
            print(f"❌ Speech Interruption Error: {e}")
    
    def _clean_text_for_tts(self, text: str) -> str:
        """Clean text for better TTS quality"""
        try:
            # Remove problematic characters
            text = text.replace('"', "'")
            text = text.replace('`', "'")
            text = text.replace('\n', ' ')
            text = text.replace('\r', ' ')
            text = text.replace('\t', ' ')
            
            # Remove multiple spaces
            while '  ' in text:
                text = text.replace('  ', ' ')
            
            # Ensure proper sentence ending
            text = text.strip()
            if text and not text.endswith(('.', '!', '?')):
                text += '.'
            
            return text
            
        except Exception as e:
            print(f"❌ Text Cleaning Error: {e}")
            return text
    
    def is_currently_speaking(self) -> bool:
        """Check if TTS is currently speaking"""
        return self.is_speaking
    
    def get_voice_info(self) -> Dict[str, Any]:
        """Get information about current voice setup"""
        return {
            "primary_tts": "Coqui TTS 2025" if self.tts_model else "Edge TTS",
            "model": self.config.tts_model if self.tts_model else self.edge_tts_voice,
            "device": self.device,
            "interruption_support": True,
            "parallel_processing": self.config.parallel_processing
        }
