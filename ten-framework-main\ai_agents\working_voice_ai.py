#!/usr/bin/env python3
"""
WORKING VOICE AI - USING EXISTING COMPONENTS
Stop making new shit - use what already works!
"""

import sys
import time
import warnings
warnings.filterwarnings("ignore")

# Import the WORKING modules we already have
from modules.perfect_stt import PerfectSTT
from modules.fast_ai_client import FastAIClient
from modules.fixed_audio_playback import FixedAudioPlayback
from modules.clean_config import CleanVoiceAIConfig

# TTS imports
try:
    import ChatTTS
    CHATTTS_AVAILABLE = True
except ImportError:
    CHATTTS_AVAILABLE = False

try:
    import edge_tts
    import asyncio
    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False

import numpy as np
import subprocess

class WorkingVoiceAI:
    """Use the shit that actually works"""
    
    def __init__(self):
        print("🔥 WORKING VOICE AI - USING EXISTING COMPONENTS")
        print("=" * 60)
        print("✅ Using PerfectSTT (already works)")
        print("✅ Using FastAIClient (already works)")  
        print("✅ Using FixedAudioPlayback (already works)")
        print("✅ Using CleanConfig (already works)")
        print("=" * 60)
        
        # Load config
        self.config = CleanVoiceAIConfig()
        
        # Initialize WORKING components
        print("🎤 Loading Perfect STT...")
        self.stt = PerfectSTT(self.config)
        
        print("🤖 Loading Fast AI Client...")
        self.ai_client = FastAIClient(self.config)
        
        print("🔊 Loading Fixed Audio Playback...")
        self.audio_playback = FixedAudioPlayback()
        
        # Initialize TTS
        self._init_working_tts()
        
        print("🚀 WORKING VOICE AI READY!")
        print("=" * 60)
        
    def _init_working_tts(self):
        """Initialize TTS that actually works"""
        
        self.tts_method = None
        
        # Try ChatTTS first
        if CHATTTS_AVAILABLE:
            print("🎭 Loading ChatTTS...")
            try:
                self.chattts = ChatTTS.Chat()
                self.chattts.load(compile=False, source="huggingface")
                self.tts_method = "chattts"
                print("✅ ChatTTS ready")
            except Exception as e:
                print(f"❌ ChatTTS failed: {e}")
                
        # Try Edge-TTS fallback
        if not self.tts_method and EDGE_TTS_AVAILABLE:
            print("🌐 Using Edge-TTS...")
            self.tts_method = "edge"
            print("✅ Edge-TTS ready")
            
        # Final fallback - Windows TTS
        if not self.tts_method:
            print("🪟 Using Windows TTS...")
            self.tts_method = "windows"
            print("✅ Windows TTS ready")
            
    def speak(self, text: str) -> bool:
        """Speak using working TTS"""
        
        if not text:
            return False
            
        print(f"🎵 Speaking: '{text[:50]}...'")
        
        try:
            if self.tts_method == "chattts":
                return self._speak_chattts(text)
            elif self.tts_method == "edge":
                return self._speak_edge_tts(text)
            else:
                return self._speak_windows(text)
                
        except Exception as e:
            print(f"❌ TTS error: {e}")
            return False
            
    def _speak_chattts(self, text: str) -> bool:
        """ChatTTS speech"""
        try:
            # Generate audio
            audio_arrays = self.chattts.infer([text])
            
            if audio_arrays and len(audio_arrays) > 0:
                audio_data = audio_arrays[0]
                
                # Convert to proper format
                audio_data = np.array(audio_data, dtype=np.float32)
                audio_data = audio_data / np.max(np.abs(audio_data))
                
                # Play using fixed audio playback
                success = self.audio_playback.play_audio_array(audio_data, self.config.sample_rate)
                
                if success:
                    print("✅ ChatTTS speech completed")
                    return True
                    
        except Exception as e:
            print(f"❌ ChatTTS error: {e}")
            
        return False
        
    def _speak_edge_tts(self, text: str) -> bool:
        """Edge-TTS speech"""
        try:
            async def generate_speech():
                communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
                audio_data = b""
                async for chunk in communicate.stream():
                    if chunk["type"] == "audio":
                        audio_data += chunk["data"]
                return audio_data
                
            # Generate speech
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            audio_data = loop.run_until_complete(generate_speech())
            loop.close()
            
            if audio_data:
                # Play using fixed audio playback
                success = self.audio_playback.play_audio_bytes(audio_data)
                
                if success:
                    print("✅ Edge-TTS speech completed")
                    return True
                    
        except Exception as e:
            print(f"❌ Edge-TTS error: {e}")
            
        return False
        
    def _speak_windows(self, text: str) -> bool:
        """Windows TTS fallback"""
        try:
            # Clean text
            clean_text = text.replace('"', "'").replace('\n', ' ')
            
            # Use Windows SAPI
            command = f'powershell -Command "Add-Type -AssemblyName System.Speech; $synth = New-Object System.Speech.Synthesis.SpeechSynthesizer; $synth.SelectVoice(\'Microsoft Zira Desktop\'); $synth.Speak(\'{clean_text}\')"'
            
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ Windows TTS completed")
                return True
            else:
                print(f"❌ Windows TTS failed: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Windows TTS error: {e}")
            
        return False
        
    def start_conversation(self):
        """Start conversation with working components"""
        
        print("\n🎙️ STARTING WORKING VOICE CONVERSATION")
        print("=" * 50)
        print("💬 Ready for conversation!")
        print("   (Say 'exit' to quit)")
        
        conversation_count = 0
        
        try:
            while True:
                conversation_count += 1
                print(f"\n--- Conversation {conversation_count} ---")
                
                # Step 1: Listen using Perfect STT
                print("🎧 Listening...")
                start_time = time.time()
                user_speech = self.stt.listen_for_speech()
                listen_time = time.time() - start_time
                
                if not user_speech:
                    print("❌ No speech detected")
                    continue
                    
                print(f"👤 You said: '{user_speech}' ({listen_time:.1f}s)")
                
                # Check for exit
                if any(word in user_speech.lower() for word in ['exit', 'quit', 'goodbye', 'stop']):
                    print("👋 Goodbye!")
                    self.speak("Goodbye! It was nice talking with you.")
                    break
                    
                # Step 2: Get AI response using Fast AI Client
                print("🤖 Getting AI response...")
                start_time = time.time()
                ai_response = self.ai_client.get_response(user_speech)
                ai_time = time.time() - start_time
                
                if not ai_response:
                    ai_response = "I'm sorry, I didn't understand that."
                    
                print(f"🤖 AI responded: '{ai_response}' ({ai_time:.1f}s)")
                
                # Step 3: Speak using working TTS
                print("🎵 Speaking response...")
                start_time = time.time()
                success = self.speak(ai_response)
                tts_time = time.time() - start_time
                
                if success:
                    total_time = listen_time + ai_time + tts_time
                    print(f"✅ Conversation complete! Total: {total_time:.1f}s")
                else:
                    print("❌ Speech failed")
                    
        except KeyboardInterrupt:
            print("\n👋 Conversation ended by user")
            
        except Exception as e:
            print(f"\n❌ Conversation error: {e}")
            
        finally:
            self._cleanup()
            
    def _cleanup(self):
        """Cleanup"""
        try:
            if hasattr(self, 'stt'):
                self.stt.cleanup()
            if hasattr(self, 'ai_client'):
                self.ai_client.cleanup()
            if hasattr(self, 'audio_playback'):
                self.audio_playback.cleanup()
            print("🧹 Cleanup completed")
        except Exception as e:
            print(f"⚠️ Cleanup error: {e}")

def main():
    """Main entry point"""
    
    try:
        # Use the working components we already have
        voice_ai = WorkingVoiceAI()
        voice_ai.start_conversation()
        
    except KeyboardInterrupt:
        print("\n👋 Exiting...")
        
    except Exception as e:
        print(f"\n❌ System error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
