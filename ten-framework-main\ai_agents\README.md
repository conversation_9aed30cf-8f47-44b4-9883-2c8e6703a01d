# Clean Voice AI - Modular Architecture

Premium voice AI system with clean, organized modular architecture.

## Features

🎵 **ChatterboxTTS** - Premium local TTS (ElevenLabs alternative)  
🎤 **HuBERT VAD** - State-of-the-art voice activity detection  
🗣️ **Whisper STT** - Accurate speech recognition  
🧠 **AgenticSeek** - Local LLM processing  
🛑 **Perfect Interruption** - Real-time speech interruption  
🚀 **CUDA Accelerated** - GPU optimization  

## Architecture

```
clean_voice_ai.py          # Main orchestrator
├── modules/
│   ├── config.py          # Clean configuration
│   ├── tts_engine.py      # ChatterboxTTS + Windows TTS
│   ├── vad_engine.py      # HuBERT VAD + WebRTC VAD
│   ├── stt_engine.py      # Whisper speech recognition
│   └── agenticseek_client.py  # LLM API client
└── check_dependencies.py  # Dependency verification
```

## Quick Start

1. **Check Dependencies**
   ```bash
   python check_dependencies.py
   ```

2. **Start AgenticSeek** (if not running)
   ```bash
   cd ../agenticSeek-main
   python api.py
   ```

3. **Run Clean Voice AI**
   ```bash
   python clean_voice_ai.py
   ```

## System Requirements

- Python 3.12+
- CUDA-capable GPU (recommended)
- 8GB+ RAM
- Microphone and speakers

## Dependencies

**Core**: torch, torchaudio, numpy, sounddevice, pygame, requests, webrtcvad  
**AI Models**: whisper, transformers, chatterbox-tts  
**Services**: AgenticSeek API (localhost:7777)  

## Usage

1. System initializes all components
2. Speaks welcome message
3. Listens for voice input
4. Processes through AgenticSeek LLM
5. Responds with natural speech
6. Supports real-time interruption
7. Say "goodbye" to exit

## Status Indicators

✅ All systems ready  
🎤 Listening for speech  
🗣️ Speaking detected  
🧠 Processing query  
🛑 Interruption detected  

## Troubleshooting

- **No CUDA**: System automatically falls back to CPU
- **No ChatterboxTTS**: Falls back to Windows TTS
- **No HuBERT VAD**: Falls back to WebRTC VAD
- **AgenticSeek offline**: Check if API is running on port 7777

## Clean Architecture Benefits

- **Modular**: Each component is separate and testable
- **Organized**: Clear separation of concerns
- **Maintainable**: Easy to update individual components
- **Robust**: Multiple fallback systems
- **Efficient**: Optimized for performance and quality
