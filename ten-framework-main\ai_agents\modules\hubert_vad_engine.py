#!/usr/bin/env python3
"""
HuBERT VAD ENGINE - Premium Voice Activity Detection
Advanced voice activity detection using HuBERT model
"""

import asyncio
import numpy as np
import torch
import webrtcvad
import pyaudio
import threading
import time
from typing import Optional, Callable
import warnings
warnings.filterwarnings("ignore")

class HuBERTVADEngine:
    """Premium Voice Activity Detection using HuBERT model"""
    
    def __init__(self, config):
        self.config = config
        self.device = config.device
        
        # Audio settings
        self.sample_rate = config.sample_rate
        self.chunk_size = config.chunk_size
        self.channels = config.channels
        
        # VAD settings
        self.vad_threshold = getattr(config, 'hubert_threshold', 0.5)
        self.silence_duration = getattr(config, 'silence_duration_ms', 400) / 1000.0
        self.interruption_check = getattr(config, 'interruption_check_ms', 50) / 1000.0
        
        # Initialize systems
        self._init_hubert_vad()
        self._init_webrtc_fallback()
        self._init_audio_system()
        
        # State
        self.is_listening = False
        self.audio_buffer = []
        self.last_speech_time = 0
        self.speech_detected = False
        
        print("🎤 HuBERT VAD Engine initialized")
        
    def _init_hubert_vad(self):
        """Initialize HuBERT VAD model"""
        try:
            # For now, we'll use a simplified approach
            # In a full implementation, you would load the actual HuBERT model
            self.hubert_available = False
            print("⚠️ HuBERT VAD model not fully implemented - using WebRTC fallback")
            
        except Exception as e:
            print(f"❌ HuBERT VAD initialization failed: {e}")
            self.hubert_available = False
            
    def _init_webrtc_fallback(self):
        """Initialize WebRTC VAD as fallback"""
        try:
            self.webrtc_vad = webrtcvad.Vad(self.config.vad_aggressiveness)
            self.webrtc_available = True
            print("✅ WebRTC VAD fallback ready")
            
        except Exception as e:
            print(f"❌ WebRTC VAD initialization failed: {e}")
            self.webrtc_available = False
            
    def _init_audio_system(self):
        """Initialize audio input system"""
        try:
            self.audio = pyaudio.PyAudio()
            
            # Find best input device
            self.input_device_index = self._find_best_input_device()
            
            print(f"🎤 Audio system ready - Device: {self.input_device_index}")
            
        except Exception as e:
            print(f"❌ Audio system initialization failed: {e}")
            self.audio = None
            
    def _find_best_input_device(self) -> Optional[int]:
        """Find the best audio input device"""
        try:
            default_device = self.audio.get_default_input_device_info()
            return default_device['index']
            
        except Exception as e:
            print(f"⚠️ Using default audio device: {e}")
            return None
            
    def detect_voice_activity(self, audio_data: np.ndarray) -> bool:
        """Detect voice activity in audio data"""
        try:
            # Convert to appropriate format for VAD
            if audio_data.dtype != np.int16:
                audio_data = (audio_data * 32767).astype(np.int16)
                
            # Try HuBERT first (if available)
            if self.hubert_available:
                return self._hubert_vad_detect(audio_data)
                
            # Fallback to WebRTC VAD
            elif self.webrtc_available:
                return self._webrtc_vad_detect(audio_data)
                
            # Energy-based fallback
            else:
                return self._energy_vad_detect(audio_data)
                
        except Exception as e:
            print(f"❌ VAD detection error: {e}")
            return False
            
    def _hubert_vad_detect(self, audio_data: np.ndarray) -> bool:
        """HuBERT-based voice activity detection"""
        # Placeholder for actual HuBERT implementation
        # This would use the loaded HuBERT model to detect speech
        return self._energy_vad_detect(audio_data)
        
    def _webrtc_vad_detect(self, audio_data: np.ndarray) -> bool:
        """WebRTC-based voice activity detection"""
        try:
            # WebRTC VAD requires specific frame sizes
            frame_duration = 30  # ms
            frame_size = int(self.sample_rate * frame_duration / 1000)
            
            # Process audio in frames
            for i in range(0, len(audio_data) - frame_size, frame_size):
                frame = audio_data[i:i + frame_size]
                
                # Convert to bytes
                frame_bytes = frame.tobytes()
                
                # Check if frame contains speech
                if self.webrtc_vad.is_speech(frame_bytes, self.sample_rate):
                    return True
                    
            return False
            
        except Exception as e:
            print(f"❌ WebRTC VAD error: {e}")
            return self._energy_vad_detect(audio_data)
            
    def _energy_vad_detect(self, audio_data: np.ndarray) -> bool:
        """Energy-based voice activity detection"""
        try:
            # Calculate RMS energy
            rms = np.sqrt(np.mean(audio_data.astype(float) ** 2))
            
            # Dynamic threshold based on recent audio
            threshold = getattr(self.config, 'voice_threshold', 0.1)
            
            return rms > threshold
            
        except Exception as e:
            print(f"❌ Energy VAD error: {e}")
            return False
            
    async def listen_for_speech_async(self) -> Optional[np.ndarray]:
        """Asynchronously listen for speech"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.listen_for_speech
        )
        
    def listen_for_speech(self) -> Optional[np.ndarray]:
        """Listen for speech and return audio data"""
        if not self.audio:
            print("❌ Audio system not available")
            return None
            
        try:
            self.is_listening = True
            audio_buffer = []
            silence_start = None
            speech_detected = False
            
            # Open audio stream
            stream = self.audio.open(
                format=pyaudio.paFloat32,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.input_device_index,
                frames_per_buffer=self.chunk_size
            )
            
            print("🎧 Listening for speech...")
            
            while self.is_listening:
                try:
                    # Read audio chunk
                    audio_chunk = stream.read(self.chunk_size, exception_on_overflow=False)
                    audio_data = np.frombuffer(audio_chunk, dtype=np.float32)
                    
                    # Detect voice activity
                    has_speech = self.detect_voice_activity(audio_data)
                    
                    if has_speech:
                        # Speech detected
                        if not speech_detected:
                            print("🗣️ Speech detected")
                            speech_detected = True
                            
                        audio_buffer.extend(audio_data)
                        silence_start = None
                        self.last_speech_time = time.time()
                        
                    else:
                        # No speech detected
                        if speech_detected:
                            # We were hearing speech, now silence
                            if silence_start is None:
                                silence_start = time.time()
                                
                            # Check if silence duration exceeded threshold
                            silence_duration = time.time() - silence_start
                            if silence_duration >= self.silence_duration:
                                print("🔇 Speech ended")
                                break
                                
                        # Always add to buffer during active listening
                        if speech_detected:
                            audio_buffer.extend(audio_data)
                            
                    # Check for timeout
                    if speech_detected and time.time() - self.last_speech_time > 10.0:
                        print("⏰ Speech timeout")
                        break
                        
                    # Small delay for responsiveness
                    time.sleep(0.01)
                    
                except Exception as e:
                    print(f"❌ Audio read error: {e}")
                    break
                    
            # Close stream
            stream.stop_stream()
            stream.close()
            
            # Return audio data if we got speech
            if audio_buffer and speech_detected:
                audio_array = np.array(audio_buffer, dtype=np.float32)
                print(f"🎤 Captured {len(audio_array)/self.sample_rate:.2f}s of audio")
                return audio_array
            else:
                return None
                
        except Exception as e:
            print(f"❌ Speech listening error: {e}")
            return None
        finally:
            self.is_listening = False
            
    def start_continuous_listening(self, callback: Callable[[np.ndarray], None]):
        """Start continuous listening with callback"""
        def listen_thread():
            while self.is_listening:
                audio_data = self.listen_for_speech()
                if audio_data is not None:
                    callback(audio_data)
                    
        self.is_listening = True
        thread = threading.Thread(target=listen_thread, daemon=True)
        thread.start()
        
        return thread
        
    def stop_listening(self):
        """Stop listening"""
        self.is_listening = False
        print("🛑 VAD listening stopped")
        
    def get_vad_status(self) -> dict:
        """Get VAD system status"""
        return {
            'hubert_available': self.hubert_available,
            'webrtc_available': self.webrtc_available,
            'audio_available': self.audio is not None,
            'is_listening': self.is_listening,
            'sample_rate': self.sample_rate,
            'chunk_size': self.chunk_size,
            'vad_threshold': self.vad_threshold,
            'silence_duration': self.silence_duration
        }
        
    def test_vad_system(self) -> bool:
        """Test VAD system functionality"""
        try:
            # Test audio system
            if not self.audio:
                return False
                
            # Test VAD with dummy data
            dummy_audio = np.random.randn(1024).astype(np.float32) * 0.1
            result = self.detect_voice_activity(dummy_audio)
            
            print(f"✅ VAD system test: {'PASS' if isinstance(result, bool) else 'FAIL'}")
            return isinstance(result, bool)
            
        except Exception as e:
            print(f"❌ VAD system test failed: {e}")
            return False
            
    def __del__(self):
        """Cleanup resources"""
        try:
            if hasattr(self, 'audio') and self.audio:
                self.audio.terminate()
        except:
            pass
