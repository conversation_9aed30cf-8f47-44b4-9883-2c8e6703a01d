"""
AgenticSeek Bridge Extension for TEN Framework

This extension bridges TEN Framework with AgenticSeek to provide:
- Local AI research capabilities
- Web browsing automation
- Multi-agent query routing
- Privacy-focused operation
"""

import asyncio
import json
import os
import sys
import traceback
from typing import Any, Dict, Optional

import aiohttp
import requests
from ten import (
    AsyncExtension,
    AsyncTenEnv,
    Cmd,
    CmdResult,
    Data,
    StatusCode,
    TenError,
)


class AgenticSeekBridgeExtension(AsyncExtension):
    """Bridge extension connecting TEN Framework to AgenticSeek"""
    
    def __init__(self, name: str) -> None:
        super().__init__(name)
        self.agenticseek_url = "http://localhost:8080"
        self.api_key = "local"
        self.timeout = 30
        self.max_retries = 3
        self.work_dir = "/tmp/agenticseek_bridge"
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def on_init(self, ten_env: AsyncTenEnv) -> None:
        """Initialize the extension"""
        ten_env.log_info("AgenticSeek Bridge Extension initializing...")
        
        # Load configuration
        self.agenticseek_url = ten_env.get_property_string("agenticseek_url") or self.agenticseek_url
        self.api_key = ten_env.get_property_string("agenticseek_api_key") or self.api_key
        self.timeout = ten_env.get_property_int("timeout_seconds") or self.timeout
        self.max_retries = ten_env.get_property_int("max_retries") or self.max_retries
        self.work_dir = ten_env.get_property_string("work_dir") or self.work_dir
        
        # Create work directory
        os.makedirs(self.work_dir, exist_ok=True)
        
        # Initialize HTTP session
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        
        ten_env.log_info(f"AgenticSeek Bridge initialized - URL: {self.agenticseek_url}")
        
    async def on_deinit(self, ten_env: AsyncTenEnv) -> None:
        """Cleanup resources"""
        if self.session:
            await self.session.close()
        ten_env.log_info("AgenticSeek Bridge Extension deinitialized")
        
    async def on_cmd(self, ten_env: AsyncTenEnv, cmd: Cmd) -> None:
        """Handle incoming commands"""
        cmd_name = cmd.get_name()
        ten_env.log_info(f"Received command: {cmd_name}")
        
        try:
            if cmd_name == "text_data":
                await self._handle_text_query(ten_env, cmd)
            else:
                ten_env.log_warn(f"Unknown command: {cmd_name}")
                await ten_env.return_result(CmdResult.create(StatusCode.ERROR), cmd)
                
        except Exception as e:
            ten_env.log_error(f"Error handling command {cmd_name}: {str(e)}")
            ten_env.log_error(traceback.format_exc())
            await ten_env.return_result(CmdResult.create(StatusCode.ERROR), cmd)
            
    async def on_data(self, ten_env: AsyncTenEnv, data: Data) -> None:
        """Handle incoming data"""
        data_name = data.get_name()
        ten_env.log_info(f"Received data: {data_name}")
        
        try:
            if data_name == "text_data":
                await self._handle_text_data(ten_env, data)
            else:
                ten_env.log_warn(f"Unknown data type: {data_name}")
                
        except Exception as e:
            ten_env.log_error(f"Error handling data {data_name}: {str(e)}")
            ten_env.log_error(traceback.format_exc())
            
    async def _handle_text_query(self, ten_env: AsyncTenEnv, cmd: Cmd) -> None:
        """Handle text query command"""
        text = cmd.get_property_string("text")
        if not text:
            ten_env.log_error("No text provided in command")
            await ten_env.return_result(CmdResult.create(StatusCode.ERROR), cmd)
            return
            
        ten_env.log_info(f"Processing query: {text[:100]}...")
        
        # Send query to AgenticSeek
        response = await self._query_agenticseek(ten_env, text)
        
        if response:
            # Create response command
            result_cmd = Cmd.create("text_data")
            result_cmd.set_property_string("text", response)
            
            # Send response
            await ten_env.send_cmd(result_cmd)
            await ten_env.return_result(CmdResult.create(StatusCode.OK), cmd)
        else:
            await ten_env.return_result(CmdResult.create(StatusCode.ERROR), cmd)
            
    async def _handle_text_data(self, ten_env: AsyncTenEnv, data: Data) -> None:
        """Handle text data"""
        text = data.get_property_string("text")
        if not text:
            ten_env.log_warn("No text in data")
            return
            
        ten_env.log_info(f"Processing data: {text[:100]}...")
        
        # Send query to AgenticSeek
        response = await self._query_agenticseek(ten_env, text)
        
        if response:
            # Create response data
            result_data = Data.create("text_data")
            result_data.set_property_string("text", response)
            
            # Send response
            await ten_env.send_data(result_data)
            
    async def _query_agenticseek(self, ten_env: AsyncTenEnv, query: str) -> Optional[str]:
        """Send query to AgenticSeek and get response"""
        if not self.session:
            ten_env.log_error("HTTP session not initialized")
            return None
            
        for attempt in range(self.max_retries):
            try:
                # Prepare request payload
                payload = {
                    "query": query,
                    "no_browser": False,  # Enable browser automation
                    "local_mode": True,
                    "work_dir": self.work_dir
                }
                
                # Send request to AgenticSeek API
                async with self.session.post(
                    f"{self.agenticseek_url}/api/query",
                    json=payload,
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {self.api_key}"
                    }
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        answer = result.get("answer", "")
                        ten_env.log_info(f"AgenticSeek response received: {len(answer)} chars")
                        return answer
                    else:
                        ten_env.log_error(f"AgenticSeek API error: {response.status}")
                        error_text = await response.text()
                        ten_env.log_error(f"Error details: {error_text}")
                        
            except Exception as e:
                ten_env.log_error(f"Attempt {attempt + 1} failed: {str(e)}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1)  # Wait before retry
                    
        ten_env.log_error("All attempts to query AgenticSeek failed")
        return None
