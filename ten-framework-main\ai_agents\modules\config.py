#!/usr/bin/env python3
"""
Configuration Module - Clean and Organized
All configuration settings for the voice AI system
"""

import torch
from dataclasses import dataclass
from typing import Optional

@dataclass
class VoiceAIConfig:
    """Clean configuration for Voice AI system"""
    
    # Core Settings
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    sample_rate: int = 16000
    chunk_size: int = 1024
    channels: int = 1
    
    # ChatterboxTTS Settings
    use_chatterbox: bool = True
    chatterbox_model: str = "resemble-ai/chatterbox-0.5b"
    
    # HuBERT VAD Settings  
    use_hubert_vad: bool = True
    hubert_vad_model: str = "HuBERT-11/distilzen-wav2vec-large-st9-mel"
    
    # Whisper STT Settings
    whisper_model: str = "large-v3"
    
    # VAD Settings
    vad_aggressiveness: int = 3
    silence_duration_ms: int = 400
    interruption_check_ms: int = 50
    
    # AgenticSeek Settings
    agenticseek_url: str = "http://localhost:7777"
    agenticseek_timeout: int = 15
    
    # Audio Settings
    output_sample_rate: int = 22050
    voice_threshold: float = 0.1
    
    def __post_init__(self):
        """Validate configuration"""
        if not torch.cuda.is_available() and self.device == "cuda":
            print("⚠️ CUDA not available, switching to CPU")
            self.device = "cpu"
