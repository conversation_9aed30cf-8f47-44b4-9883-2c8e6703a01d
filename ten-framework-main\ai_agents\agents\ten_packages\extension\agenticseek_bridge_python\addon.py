"""
AgenticSeek Bridge Extension Addon

This addon registers the AgenticSeek Bridge Extension with TEN Framework
"""

from ten import Addon, register_addon_as_extension, TenEnv
from .extension import AgenticSeekBridgeExtension


@register_addon_as_extension("agenticseek_bridge_python")
class AgenticSeekBridgeExtensionAddon(Addon):
    """Addon for AgenticSeek Bridge Extension"""
    
    def on_create_instance(self, ten_env: TenEnv, name: str, context) -> None:
        """Create extension instance"""
        ten_env.log_info(f"Creating AgenticSeek Bridge Extension instance: {name}")
        extension = AgenticSeekBridgeExtension(name)
        ten_env.on_create_instance_done(extension, context)
