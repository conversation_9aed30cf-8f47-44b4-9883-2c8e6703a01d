#!/usr/bin/env python3
"""
CLEAN VOICE AI SYSTEM
Step-by-step perfect implementation:
1. ✅ Clean system & remove junk
2. ✅ Perfect speech-to-text
3. ✅ Fast processing
4. ✅ Amazing natural voice
"""

import sys
import time
import signal
import warnings
warnings.filterwarnings("ignore")

# Import our clean modules
from modules.clean_config import CleanVoiceAIConfig
from modules.perfect_stt import PerfectSTT
from modules.fast_ai_client import FastAIClient
from modules.amazing_tts import AmazingTTS

class CleanVoiceAISystem:
    """Clean, fast, amazing voice AI system"""
    
    def __init__(self):
        print("🎙️ CLEAN VOICE AI SYSTEM")
        print("=" * 60)
        print("✅ Step 1: Clean system & remove junk")
        print("✅ Step 2: Perfect speech-to-text")
        print("✅ Step 3: Fast processing")
        print("✅ Step 4: Amazing natural voice")
        print("=" * 60)
        
        # Initialize configuration
        print("⚙️ Loading clean configuration...")
        self.config = CleanVoiceAIConfig()
        
        # Initialize components step by step
        self._init_components()
        
        # Setup signal handlers for clean shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        print("🚀 CLEAN VOICE AI READY!")
        print("=" * 60)
        
    def _init_components(self):
        """Initialize all components"""
        
        try:
            # Step 2: Perfect Speech-to-Text
            print("🎤 Initializing Perfect STT...")
            self.stt = PerfectSTT(self.config)
            
            # Step 3: Fast AI Processing
            print("🤖 Initializing Fast AI Client...")
            self.ai_client = FastAIClient(self.config)
            
            # Step 4: Amazing Voice Output
            print("🎵 Initializing Amazing TTS...")
            self.tts = AmazingTTS(self.config)
            
            print("✅ All components initialized successfully")
            
        except Exception as e:
            print(f"❌ Component initialization failed: {e}")
            sys.exit(1)
            
    def run_system_tests(self) -> bool:
        """Run comprehensive system tests"""
        print("\n🧪 RUNNING SYSTEM TESTS")
        print("=" * 40)
        
        tests_passed = 0
        total_tests = 4
        
        # Test 1: Microphone
        print("Test 1/4: Microphone...")
        if self.stt.test_microphone():
            tests_passed += 1
            
        # Test 2: AI Connection
        print("Test 2/4: AI Connection...")
        if self.ai_client.test_ai_response():
            tests_passed += 1
            
        # Test 3: Voice Output
        print("Test 3/4: Voice Output...")
        if self.tts.test_voice():
            tests_passed += 1
            
        # Test 4: Quick Speech Test
        print("Test 4/4: Quick Speech Test...")
        try:
            print("🎤 Say 'test' now...")
            speech = self.stt.listen_for_speech()
            if speech and len(speech) > 0:
                print(f"✅ Heard: '{speech}'")
                tests_passed += 1
            else:
                print("❌ No speech detected")
        except Exception as e:
            print(f"❌ Speech test failed: {e}")
            
        print("=" * 40)
        print(f"🧪 TESTS COMPLETED: {tests_passed}/{total_tests} passed")
        
        if tests_passed >= 3:
            print("✅ System ready for conversation!")
            return True
        else:
            print("❌ System has issues - check configuration")
            return False
            
    def start_conversation(self):
        """Start the main conversation loop"""
        
        if not self.run_system_tests():
            print("❌ System tests failed - cannot start conversation")
            return
            
        print("\n🎙️ STARTING CLEAN VOICE CONVERSATION")
        print("=" * 50)
        print("Features:")
        print("  ✅ Perfect Speech Recognition")
        print("  ✅ Fast AI Processing")
        print("  ✅ Amazing Natural Voice")
        print("  ✅ Clean, Minimal System")
        print("=" * 50)
        print("💬 Ready for conversation! Say something...")
        print("   (Press Ctrl+C to exit)")
        
        conversation_count = 0
        
        try:
            while True:
                conversation_count += 1
                print(f"\n--- Conversation {conversation_count} ---")
                
                # Step 1: Listen for speech
                print("🎧 Listening...")
                user_speech = self.stt.listen_for_speech()
                
                if not user_speech:
                    print("❌ No speech detected, trying again...")
                    continue
                    
                print(f"👤 You said: '{user_speech}'")
                
                # Check for exit commands
                if any(word in user_speech.lower() for word in ['exit', 'quit', 'goodbye', 'stop']):
                    print("👋 Goodbye!")
                    self.tts.speak("Goodbye! It was nice talking with you.")
                    break
                    
                # Step 2: Get AI response
                print("🤖 Thinking...")
                ai_response = self.ai_client.get_response(user_speech)
                
                if not ai_response:
                    ai_response = "I'm sorry, I didn't understand that. Could you please repeat?"
                    
                print(f"🤖 AI: {ai_response}")
                
                # Step 3: Speak response
                print("🎵 Speaking...")
                success = self.tts.speak(ai_response)
                
                if not success:
                    print("❌ Speech output failed")
                    
                print("✅ Conversation cycle complete")
                
        except KeyboardInterrupt:
            print("\n👋 Conversation ended by user")
            
        except Exception as e:
            print(f"\n❌ Conversation error: {e}")
            
        finally:
            self._cleanup()
            
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\n🛑 Received signal {signum}, shutting down...")
        self._cleanup()
        sys.exit(0)
        
    def _cleanup(self):
        """Clean shutdown"""
        print("\n🧹 Cleaning up...")
        
        try:
            if hasattr(self, 'stt'):
                self.stt.cleanup()
                
            if hasattr(self, 'ai_client'):
                self.ai_client.cleanup()
                
            if hasattr(self, 'tts'):
                self.tts.cleanup()
                
            print("✅ Cleanup completed")
            
        except Exception as e:
            print(f"⚠️ Cleanup error: {e}")

def main():
    """Main entry point"""
    
    try:
        # Create and run the clean voice AI system
        voice_ai = CleanVoiceAISystem()
        voice_ai.start_conversation()
        
    except KeyboardInterrupt:
        print("\n👋 Exiting...")
        
    except Exception as e:
        print(f"\n❌ System error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
