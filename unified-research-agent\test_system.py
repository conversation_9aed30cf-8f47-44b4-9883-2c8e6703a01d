#!/usr/bin/env python3
"""
UNIFIED RESEARCH AGENT - SYSTEM TESTING
Tests all components step by step to ensure everything works
"""

import asyncio
import logging
import sys
import traceback
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class SystemTester:
    """Comprehensive system testing for unified research agent"""
    
    def __init__(self):
        self.tests_passed = 0
        self.tests_failed = 0
        self.test_results = {}
    
    async def run_all_tests(self):
        """Run all system tests"""
        logger.info("=" * 60)
        logger.info("UNIFIED RESEARCH AGENT - SYSTEM TESTING")
        logger.info("=" * 60)
        
        # Test 1: Basic imports
        await self.test_basic_imports()
        
        # Test 2: Ollama connection
        await self.test_ollama_connection()
        
        # Test 3: Hardware detection
        await self.test_hardware_detection()
        
        # Test 4: Model router
        await self.test_model_router()
        
        # Test 5: System coordinator
        await self.test_system_coordinator()
        
        # Test 6: Research orchestrator
        await self.test_research_orchestrator()
        
        # Test 7: Audio system
        await self.test_audio_system()
        
        # Final report
        self.print_final_report()
    
    async def test_basic_imports(self):
        """Test that all modules can be imported"""
        logger.info("Test 1: Basic Imports")
        
        try:
            # Test core imports
            import torch
            import numpy as np
            logger.info(f"  PyTorch: {torch.__version__}")
            logger.info(f"  NumPy: {np.__version__}")
            
            # Test audio imports
            import pyaudio
            logger.info("  PyAudio: Available")
            
            # Test Ollama
            import ollama
            logger.info("  Ollama: Available")
            
            # Test our modules
            from model_router import ModelRouter
            from system_coordinator import SystemCoordinator
            from research_orchestrator import ResearchOrchestrator
            logger.info("  Core modules: Available")
            
            self.tests_passed += 1
            self.test_results["basic_imports"] = "PASSED"
            logger.info("  ✅ Basic imports test PASSED")
            
        except Exception as e:
            self.tests_failed += 1
            self.test_results["basic_imports"] = f"FAILED: {e}"
            logger.error(f"  ❌ Basic imports test FAILED: {e}")
    
    async def test_ollama_connection(self):
        """Test Ollama connection and available models"""
        logger.info("\nTest 2: Ollama Connection")
        
        try:
            import ollama
            
            # Test connection
            models = ollama.list()
            available_models = [m.model for m in models.models]
            
            logger.info(f"  Connected to Ollama: {len(available_models)} models available")
            
            # Check for key models
            key_models = ["qwen2.5vl:32b", "deepseek-r1:14b", "llama3.2:latest"]
            found_models = []
            
            for model in key_models:
                if model in available_models:
                    found_models.append(model)
                    logger.info(f"  ✅ {model}: Available")
                else:
                    logger.warning(f"  ⚠️ {model}: Not found")
            
            if len(found_models) >= 2:
                self.tests_passed += 1
                self.test_results["ollama_connection"] = "PASSED"
                logger.info("  ✅ Ollama connection test PASSED")
            else:
                self.tests_failed += 1
                self.test_results["ollama_connection"] = "FAILED: Insufficient models"
                logger.error("  ❌ Ollama connection test FAILED: Need at least 2 key models")
            
        except Exception as e:
            self.tests_failed += 1
            self.test_results["ollama_connection"] = f"FAILED: {e}"
            logger.error(f"  ❌ Ollama connection test FAILED: {e}")
    
    async def test_hardware_detection(self):
        """Test hardware detection"""
        logger.info("\nTest 3: Hardware Detection")
        
        try:
            import torch
            import psutil
            
            # GPU detection
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                logger.info(f"  GPU: {gpu_name}")
                logger.info(f"  VRAM: {gpu_memory:.1f}GB")
            else:
                logger.warning("  GPU: CUDA not available")
            
            # RAM detection
            ram_gb = psutil.virtual_memory().total / (1024**3)
            logger.info(f"  RAM: {ram_gb:.1f}GB")
            
            # CPU detection
            cpu_count = psutil.cpu_count()
            logger.info(f"  CPU Cores: {cpu_count}")
            
            self.tests_passed += 1
            self.test_results["hardware_detection"] = "PASSED"
            logger.info("  ✅ Hardware detection test PASSED")
            
        except Exception as e:
            self.tests_failed += 1
            self.test_results["hardware_detection"] = f"FAILED: {e}"
            logger.error(f"  ❌ Hardware detection test FAILED: {e}")
    
    async def test_model_router(self):
        """Test model router initialization"""
        logger.info("\nTest 4: Model Router")
        
        try:
            from model_router import ModelRouter
            
            router = ModelRouter()
            await router.initialize()
            
            status = await router.get_model_status()
            logger.info(f"  Available models: {len(status.get('available_models', []))}")
            logger.info(f"  Currently loaded: {status.get('currently_loaded', 'None')}")
            
            self.tests_passed += 1
            self.test_results["model_router"] = "PASSED"
            logger.info("  ✅ Model router test PASSED")
            
        except Exception as e:
            self.tests_failed += 1
            self.test_results["model_router"] = f"FAILED: {e}"
            logger.error(f"  ❌ Model router test FAILED: {e}")
            logger.error(f"  Error details: {traceback.format_exc()}")
    
    async def test_system_coordinator(self):
        """Test system coordinator"""
        logger.info("\nTest 5: System Coordinator")
        
        try:
            from system_coordinator import SystemCoordinator
            
            coordinator = SystemCoordinator()
            await coordinator.initialize()
            
            health = await coordinator.get_system_health()
            
            for system_name, system_health in health.items():
                status = system_health.get("status", "unknown")
                logger.info(f"  {system_name}: {status}")
            
            self.tests_passed += 1
            self.test_results["system_coordinator"] = "PASSED"
            logger.info("  ✅ System coordinator test PASSED")
            
        except Exception as e:
            self.tests_failed += 1
            self.test_results["system_coordinator"] = f"FAILED: {e}"
            logger.error(f"  ❌ System coordinator test FAILED: {e}")
            logger.error(f"  Error details: {traceback.format_exc()}")
    
    async def test_research_orchestrator(self):
        """Test research orchestrator"""
        logger.info("\nTest 6: Research Orchestrator")
        
        try:
            from model_router import ModelRouter
            from system_coordinator import SystemCoordinator
            from research_orchestrator import ResearchOrchestrator
            
            # Create components
            router = ModelRouter()
            coordinator = SystemCoordinator()
            
            # Initialize (might fail, that's ok for testing)
            try:
                await router.initialize()
                await coordinator.initialize()
            except:
                pass  # Continue with test even if initialization partially fails
            
            orchestrator = ResearchOrchestrator(router, coordinator)
            await orchestrator.initialize()
            
            logger.info("  Research orchestrator initialized successfully")
            
            self.tests_passed += 1
            self.test_results["research_orchestrator"] = "PASSED"
            logger.info("  ✅ Research orchestrator test PASSED")
            
        except Exception as e:
            self.tests_failed += 1
            self.test_results["research_orchestrator"] = f"FAILED: {e}"
            logger.error(f"  ❌ Research orchestrator test FAILED: {e}")
            logger.error(f"  Error details: {traceback.format_exc()}")
    
    async def test_audio_system(self):
        """Test audio system capabilities"""
        logger.info("\nTest 7: Audio System")
        
        try:
            import pyaudio
            
            # Test PyAudio initialization
            audio = pyaudio.PyAudio()
            
            # Find audio devices
            input_devices = []
            output_devices = []
            
            for i in range(audio.get_device_count()):
                try:
                    device_info = audio.get_device_info_by_index(i)
                    if device_info.get('maxInputChannels', 0) > 0:
                        input_devices.append(i)
                    if device_info.get('maxOutputChannels', 0) > 0:
                        output_devices.append(i)
                except:
                    continue
            
            logger.info(f"  Input devices found: {len(input_devices)}")
            logger.info(f"  Output devices found: {len(output_devices)}")
            
            audio.terminate()
            
            if len(input_devices) > 0 and len(output_devices) > 0:
                self.tests_passed += 1
                self.test_results["audio_system"] = "PASSED"
                logger.info("  ✅ Audio system test PASSED")
            else:
                self.tests_failed += 1
                self.test_results["audio_system"] = "FAILED: No audio devices"
                logger.error("  ❌ Audio system test FAILED: No audio devices found")
            
        except Exception as e:
            self.tests_failed += 1
            self.test_results["audio_system"] = f"FAILED: {e}"
            logger.error(f"  ❌ Audio system test FAILED: {e}")
    
    def print_final_report(self):
        """Print final test report"""
        logger.info("\n" + "=" * 60)
        logger.info("FINAL TEST REPORT")
        logger.info("=" * 60)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASSED" if result == "PASSED" else f"❌ {result}"
            logger.info(f"{test_name}: {status}")
        
        logger.info("-" * 60)
        logger.info(f"Tests Passed: {self.tests_passed}")
        logger.info(f"Tests Failed: {self.tests_failed}")
        logger.info(f"Success Rate: {(self.tests_passed/(self.tests_passed+self.tests_failed)*100):.1f}%")
        
        if self.tests_failed == 0:
            logger.info("🎉 ALL TESTS PASSED - System ready for deployment!")
        elif self.tests_passed >= self.tests_failed:
            logger.info("⚠️ MOST TESTS PASSED - System partially ready")
        else:
            logger.info("❌ MULTIPLE FAILURES - System needs fixes")

async def main():
    """Main test function"""
    tester = SystemTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main()) 