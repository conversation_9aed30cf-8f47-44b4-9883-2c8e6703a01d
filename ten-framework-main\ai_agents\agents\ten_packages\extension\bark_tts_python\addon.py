"""
Local Bark TTS Extension Addon

This addon registers the Bark TTS Extension with TEN Framework
"""

from ten import Addon, register_addon_as_extension, TenEnv
from .extension import BarkTTSExtension


@register_addon_as_extension("bark_tts_python")
class BarkTTSExtensionAddon(Addon):
    """Addon for Bark TTS Extension"""
    
    def on_create_instance(self, ten_env: TenEnv, name: str, context) -> None:
        """Create extension instance"""
        ten_env.log_info(f"Creating Bark TTS Extension instance: {name}")
        extension = BarkTTSExtension(name)
        ten_env.on_create_instance_done(extension, context)
