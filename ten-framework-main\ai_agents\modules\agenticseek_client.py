#!/usr/bin/env python3
"""
AgenticSeek Client Module - Clean LLM Integration
Handles communication with AgenticSeek API
"""

import requests
import time
import json
from typing import Optional

class AgenticSeekClient:
    """Clean AgenticSeek API client"""
    
    def __init__(self, config):
        self.config = config
        self.base_url = config.agenticseek_url
        self.timeout = config.agenticseek_timeout
        
        # Test connection
        self._test_connection()
        
    def _test_connection(self):
        """Test AgenticSeek connection"""
        print("🔄 Testing AgenticSeek connection...")
        
        try:
            response = requests.get(
                f"{self.base_url}/health",
                timeout=5
            )
            
            if response.status_code == 200:
                print("✅ AgenticSeek connected")
                self.connected = True
            else:
                print(f"⚠️ AgenticSeek health check failed: {response.status_code}")
                self.connected = False
                
        except Exception as e:
            print(f"❌ AgenticSeek connection failed: {e}")
            self.connected = False
            
    def query(self, text: str) -> Optional[str]:
        """Send query to AgenticSeek"""
        if not self.connected:
            return "Sorry, I'm not connected to the AI service right now."
            
        if not text.strip():
            return None
            
        try:
            print(f"🧠 Querying AgenticSeek: {text[:50]}...")
            
            # Prepare request
            payload = {
                "query": text,
                "stream": False
            }
            
            # Send request with rate limiting
            time.sleep(0.5)  # Prevent rate limiting
            
            response = requests.post(
                f"{self.base_url}/query",
                json=payload,
                timeout=self.timeout,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                answer = result.get("response", "").strip()
                
                if answer:
                    print(f"✅ AgenticSeek response: {answer[:50]}...")
                    return answer
                else:
                    return "I couldn't generate a response to that."
                    
            elif response.status_code == 429:
                print("⚠️ Rate limited, waiting...")
                time.sleep(2)
                return "Please wait a moment, I'm processing too many requests."
                
            else:
                print(f"❌ AgenticSeek error: {response.status_code}")
                return "Sorry, I encountered an error processing your request."
                
        except requests.exceptions.Timeout:
            print("⏰ AgenticSeek timeout")
            return "Sorry, that request took too long to process."
            
        except Exception as e:
            print(f"❌ AgenticSeek query error: {e}")
            return "Sorry, I encountered an error processing your request."
            
    def is_connected(self) -> bool:
        """Check connection status"""
        return self.connected
        
    def get_status(self) -> str:
        """Get connection status"""
        if self.connected:
            return f"Connected to {self.base_url}"
        else:
            return "Disconnected"
